package com.gg.gapo.initializer

import android.app.Application
import android.content.Context
import androidx.startup.Initializer
import com.blankj.utilcode.util.Utils

/**
 * <AUTHOR>
 * @since 01/01/2021
 */
class UtilsLibsInitializer : Initializer<Unit> {
    override fun create(context: Context) {
        Utils.init(context.applicationContext as Application)
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }
}
