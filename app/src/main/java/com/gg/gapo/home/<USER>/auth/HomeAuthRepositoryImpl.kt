package com.gg.gapo.home.data.auth

import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.home.data.auth.remote.HomeAuthRemote
import com.gg.gapo.home.data.auth.remote.response.ForceChangePasswordMapper.mapToDomainModel
import com.gg.gapo.home.domain.auth.HomeAuthRepository
import com.gg.gapo.home.domain.auth.model.ForceChangePasswordModel

/**
 * <AUTHOR>
 * @since 24/04/2024
 */
internal class HomeAuthRepositoryImpl(
    private val coroutineDispatchers: CoroutineDispatchers,
    private val homeAuthRemote: HomeAuthRemote
) : HomeAuthRepository {
    override suspend fun checkForceChangePassword(userId: String): ForceChangePasswordModel? {
        return homeAuthRemote.checkForceChangePassword(userId)?.mapToDomainModel()
    }
}
