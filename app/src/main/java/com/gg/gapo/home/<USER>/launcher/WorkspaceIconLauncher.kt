package com.gg.gapo.home.presentation.launcher

import android.app.Activity
import android.content.ComponentName
import android.content.pm.PackageManager
import com.gg.gapo.splash.SplashActivity

/**
 * <AUTHOR>
 * @since 1/5/23
 */
internal enum class WorkspaceIconLauncher {
    DIGIWORLD {
        override val workspaceIds: List<String>
            get() = listOf("582995832907655")

        override val wsName: String
            get() = "digiworld"

        override val clazzName: String
            get() = "com.gg.gapo.DigiWorldActivityAlias"
    },
    MOMOLIFE {
        override val workspaceIds: List<String>?
            get() = null

        override val wsName: String
            get() = "momo"

        override val clazzName: String
            get() = "com.gg.gapo.MomoLifeActivityAlias"
    },
    RICCO {
        override val workspaceIds: List<String>
            get() = listOf(
                "583924337632451",
                "583938026420647",
                "583938025744315",
                "583938025141410",
                "583938024509591",
                "583938023860444"
            )
        override val wsName: String
            get() = "ricco"
        override val clazzName: String
            get() = "com.gg.gapo.RiccoActivityAlias"
    },
    VIN_SCHOOL {
        override val workspaceIds: List<String>
            get() = listOf(
                "583384897070196",
                "582121726751985"
            )
        override val wsName: String
            get() = "vinschool"
        override val clazzName: String
            get() = "com.gg.gapo.VinSchoolActivityAliias"
    },
    PVCFC {
        override val workspaceIds: List<String>
            get() = listOf(
                "584974811699112"
            )
        override val wsName: String
            get() = "damcamau"
        override val clazzName: String
            get() = "com.gg.gapo.PvcfcActivityAlias"
    };

    abstract val workspaceIds: List<String>?

    abstract val wsName: String?

    abstract val clazzName: String

    companion object {
        fun find(workspaceId: String) = entries.find { it.workspaceIds?.isNotEmpty() == true && it.workspaceIds.orEmpty().contains(workspaceId) }

        fun findByName(name: String) = entries.find { it.wsName == name }
    }
}

internal fun Activity.setComponentEnabledSetting(iconLauncher: WorkspaceIconLauncher?) {
    val default = ComponentName(this, SplashActivity::class.java)

    when (iconLauncher) {
        WorkspaceIconLauncher.DIGIWORLD,
        WorkspaceIconLauncher.MOMOLIFE,
        WorkspaceIconLauncher.RICCO,
        WorkspaceIconLauncher.PVCFC,
        WorkspaceIconLauncher.VIN_SCHOOL -> {
            enableComponent(ComponentName(this, iconLauncher.clazzName))
            disableComponent(default)
            WorkspaceIconLauncher.entries.forEach {
                if (it.clazzName != iconLauncher.clazzName) {
                    disableComponent(ComponentName(this, it.clazzName))
                }
            }
        }

        else -> {
            enableComponent(default)
            WorkspaceIconLauncher.entries.forEach {
                disableComponent(ComponentName(this, it.clazzName))
            }
        }
    }
}

private fun Activity.enableComponent(componentName: ComponentName) {
    packageManager?.setComponentEnabledSetting(
        componentName,
        PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
        PackageManager.DONT_KILL_APP
    )
}

private fun Activity.disableComponent(componentName: ComponentName) {
    packageManager?.setComponentEnabledSetting(
        componentName,
        PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
        PackageManager.DONT_KILL_APP
    )
}
