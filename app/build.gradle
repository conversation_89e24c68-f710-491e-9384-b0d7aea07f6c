plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.kotlin.parcelize)
    alias(libs.plugins.ksp)
    alias(libs.plugins.androidx.navigation)
    alias(libs.plugins.firebase.perf)
    alias(libs.plugins.firebase.crashlytics)
    alias(libs.plugins.sentry)
    alias(libs.plugins.maven.version)
}

apply plugin: 'realm-android'

//./gradlew projectDependencyGraph
//apply from: 'https://raw.githubusercontent.com/JakeWharton/SdkSearch/master/gradle/projectDependencyGraph.gradle'

apply from: "$rootDir/android-flavors.gradle"
apply from: "$rootDir/gradle/local-aar.gradle"
apply from: "$rootDir/android-project-accessors.gradle"

android {
    namespace "com.gg.gapo"

    defaultConfig {
        multiDexEnabled true

        vectorDrawables.useSupportLibrary = true

        resConfigs "en", "vi"

        ksp {
            arg("deepLink.incremental", "true")
            arg("deepLink.customAnnotations", "com.gg.gapo.core.navigation.AppDeepLink|com.gg.gapo.core.navigation.WebDeepLink")
        }

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildFeatures {
        buildConfig = true
        dataBinding true
        viewBinding true
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion libs.versions.kotlinCompiler.get()
    }

    signingConfigs {
        develop {
            storeFile file("src/key/gw-dev.jks")
            storePassword "123654"
            keyAlias "gw-dev"
            keyPassword "123654"
        }
    }

    buildTypes {
        debug {
            // Disabling automatic build ID generation also allows you to use Apply Changes alongside Crashlytics for your debug builds.
            ext.alwaysUpdateBuildId = false
            manifestPlaceholders["crashlyticsEnabled"] = false
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }

            manifestPlaceholders["performanceDeactivated"] = true
            FirebasePerformance {
                // Set this flag to 'false' to disable @AddTrace annotation processing and
                // automatic monitoring of HTTP/S network requests
                // for a specific build variant at compile time.
                instrumentationEnabled false
            }

            debuggable true
            // QA chỉ cần armeabi-v7a
            ndk {
                if (project.hasProperty("only-arm")) {
                    abiFilters "armeabi-v7a"
                } else {
                    abiFilters "armeabi-v7a", "arm64-v8a", "x86_64", "x86"
                }
            }
            signingConfig signingConfigs.develop
        }

        release {
            manifestPlaceholders
            matchingFallbacks = ['debug', 'qa', 'release']
            manifestPlaceholders["crashlyticsEnabled"] = true
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
            manifestPlaceholders["performanceDeactivated"] = false

            // Flutter chỉ support 3 architectures
            // QA chỉ cần armeabi-v7a
            ndk {
                if (project.hasProperty("only-arm")) {
                    abiFilters "armeabi-v7a"
                } else {
                    abiFilters "armeabi-v7a", "arm64-v8a", "x86_64"
                }
            }

            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.develop
        }

        qa {
            initWith release
            manifestPlaceholders["performanceDeactivated"] = true
            FirebasePerformance {
                instrumentationEnabled false
            }

            signingConfig signingConfigs.develop
        }
    }

    productFlavors {
        saas {
            applicationId "work.vn.gapo.app"
        }

        onPremise {
            applicationId "workop.vn.gapo.app"
        }

        staging {
            applicationIdSuffix ".staging"
        }

        uat {
            applicationIdSuffix ".uat"
        }

        production {}
    }

    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def flavor = variant.name
            outputFileName = new File("GapoWork_${flavor}_${variant.versionName}.apk")
        }
    }

    lintOptions {
        disable 'NullSafeMutableLiveData'
        abortOnError false
        checkAllWarnings false
        checkReleaseBuilds false
        ignoreWarnings true
        quiet true
        checkDependencies true
    }

    packagingOptions {
        pickFirst 'kotlin/kotlin.kotlin_builtins'
        pickFirst 'kotlin/coroutines/coroutines.kotlin_builtins'
        pickFirst 'kotlin/reflect/reflect.kotlin_builtins'
        pickFirst 'kotlin/collections/collections.kotlin_builtins'
        pickFirst 'kotlin/annotation/annotation.kotlin_builtins'
        pickFirst 'kotlin/internal/internal.kotlin_builtins'
        pickFirst 'kotlin/ranges/ranges.kotlin_builtins'
        exclude 'META-INF/main.kotlin_module'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/*.kotlin_module'
        exclude("META-INF/jersey-module-version")
        exclude("META-INF/LICENSE.md")
        exclude("META-INF/NOTICE.md")
    }

    bundle {
        language {
            enableSplit = false
        }
    }
}

sentry {
    includeProguardMapping = true
    autoUploadProguardMapping = true
    ignoredBuildTypes = ["debug"]
    ignoredFlavors = ["onPremise", "staging", "uat"]
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation modulePath(projects.analytic)

    implementation modulePath(projects.coreGapo)
    implementation modulePath(projects.coreEventBus)
    implementation modulePath(projects.coreUtilities)
    implementation modulePath(projects.coreFeed)
    implementation modulePath(projects.coreUi)

    stagingImplementation stagingModulePath(projects.coreNavigation)
    uatImplementation uatModulePath(projects.coreNavigation)
    saasProductionImplementation saasProductionModulePath(projects.coreNavigation)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.coreNavigation)

    implementation modulePath(projects.libraryGapoMatisse)
    implementation modulePath(projects.libraryReaction)
    implementation modulePath(projects.libraryPanelAndroidx)
    implementation modulePath(projects.libraryGallery)
    implementation modulePath(projects.libraryPhotoEffects)
    implementation modulePath(projects.libraryCookieBar)
    implementation modulePath(projects.libraryQrcode)

    implementation modulePath(projects.sharedPollVote)

    stagingImplementation stagingModulePath(projects.flutterx)
    uatImplementation uatModulePath(projects.flutterx)
    saasProductionImplementation saasProductionModulePath(projects.flutterx)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.flutterx)

    stagingImplementation stagingModulePath(projects.featureSetting)
    uatImplementation uatModulePath(projects.featureSetting)
    saasProductionImplementation saasProductionModulePath(projects.featureSetting)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureSetting)

    stagingImplementation stagingModulePath(projects.featureLivestream)
    uatImplementation uatModulePath(projects.featureLivestream)
    saasProductionImplementation saasProductionModulePath(projects.featureLivestream)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureLivestream)

    stagingImplementation stagingModulePath(projects.featureSticker)
    uatImplementation uatModulePath(projects.featureSticker)
    saasProductionImplementation saasProductionModulePath(projects.featureSticker)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureSticker)

    stagingImplementation stagingModulePath(projects.featureAuthentication)
    uatImplementation uatModulePath(projects.featureAuthentication)
    saasProductionImplementation saasProductionModulePath(projects.featureAuthentication)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureAuthentication)

    stagingImplementation stagingModulePath(projects.featureGroup)
    uatImplementation uatModulePath(projects.featureGroup)
    saasProductionImplementation saasProductionModulePath(projects.featureGroup)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureGroup)

    stagingImplementation stagingModulePath(projects.featurePostDetail)
    uatImplementation uatModulePath(projects.featurePostDetail)
    saasProductionImplementation saasProductionModulePath(projects.featurePostDetail)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featurePostDetail)

    stagingImplementation stagingModulePath(projects.featureUserProfile)
    uatImplementation uatModulePath(projects.featureUserProfile)
    saasProductionImplementation saasProductionModulePath(projects.featureUserProfile)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureUserProfile)

    stagingImplementation stagingModulePath(projects.featureSearch)
    uatImplementation uatModulePath(projects.featureSearch)
    saasProductionImplementation saasProductionModulePath(projects.featureSearch)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureSearch)

    stagingImplementation stagingModulePath(projects.featureMessenger)
    uatImplementation uatModulePath(projects.featureMessenger)
    saasProductionImplementation saasProductionModulePath(projects.featureMessenger)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureMessenger)

    stagingImplementation stagingModulePath(projects.featureEditPhoto)
    uatImplementation uatModulePath(projects.featureEditPhoto)
    saasProductionImplementation saasProductionModulePath(projects.featureEditPhoto)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureEditPhoto)

    stagingImplementation stagingModulePath(projects.featureNotification)
    uatImplementation uatModulePath(projects.featureNotification)
    saasProductionImplementation saasProductionModulePath(projects.featureNotification)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureNotification)

    stagingImplementation stagingModulePath(projects.featureReport)
    uatImplementation uatModulePath(projects.featureReport)
    saasProductionImplementation saasProductionModulePath(projects.featureReport)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureReport)

    stagingImplementation stagingModulePath(projects.featureAsk)
    uatImplementation uatModulePath(projects.featureAsk)
    saasProductionImplementation saasProductionModulePath(projects.featureAsk)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureAsk)

    stagingImplementation stagingModulePath(projects.featureCall)
    uatImplementation uatModulePath(projects.featureCall)
    saasProductionImplementation saasProductionModulePath(projects.featureCall)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureCall)

    stagingImplementation stagingModulePath(projects.featurePostCreator)
    uatImplementation uatModulePath(projects.featurePostCreator)
    saasProductionImplementation saasProductionModulePath(projects.featurePostCreator)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featurePostCreator)

    stagingImplementation stagingModulePath(projects.featurePeriodicSurvey)
    uatImplementation uatModulePath(projects.featurePeriodicSurvey)
    saasProductionImplementation saasProductionModulePath(projects.featurePeriodicSurvey)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featurePeriodicSurvey)

    stagingImplementation stagingModulePath(projects.featureFeedsFollow)
    uatImplementation uatModulePath(projects.featureFeedsFollow)
    saasProductionImplementation saasProductionModulePath(projects.featureFeedsFollow)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureFeedsFollow)

    stagingImplementation stagingModulePath(projects.featureTrendingHashtag)
    uatImplementation uatModulePath(projects.featureTrendingHashtag)
    saasProductionImplementation saasProductionModulePath(projects.featureTrendingHashtag)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureTrendingHashtag)

    stagingImplementation stagingModulePath(projects.featureTooltips)
    uatImplementation uatModulePath(projects.featureTooltips)
    saasProductionImplementation saasProductionModulePath(projects.featureTooltips)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureTooltips)

    stagingImplementation stagingModulePath(projects.featureGoogleMeet)
    uatImplementation uatModulePath(projects.featureGoogleMeet)
    saasProductionImplementation saasProductionModulePath(projects.featureGoogleMeet)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureGoogleMeet)

    stagingImplementation stagingModulePath(projects.featureWorkspace)
    uatImplementation uatModulePath(projects.featureWorkspace)
    saasProductionImplementation saasProductionModulePath(projects.featureWorkspace)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureWorkspace)

    stagingImplementation stagingModulePath(projects.featureInvitationWorkspace)
    uatImplementation uatModulePath(projects.featureInvitationWorkspace)
    saasProductionImplementation saasProductionModulePath(projects.featureInvitationWorkspace)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureInvitationWorkspace)

    stagingImplementation stagingModulePath(projects.featureApproval)
    uatImplementation uatModulePath(projects.featureApproval)
    saasProductionImplementation saasProductionModulePath(projects.featureApproval)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureApproval)

    stagingImplementation stagingModulePath(projects.featureBilling)
    uatImplementation uatModulePath(projects.featureBilling)
    saasProductionImplementation saasProductionModulePath(projects.featureBilling)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureBilling)

    stagingImplementation stagingModulePath(projects.featureGoogleMeet)
    uatImplementation uatModulePath(projects.featureGoogleMeet)
    saasProductionImplementation saasProductionModulePath(projects.featureGoogleMeet)
    onPremiseProductionImplementation onPremiseProductionModulePath(projects.featureGoogleMeet)

    implementation libs.kotlin.stdlib
    implementation libs.kotlin.coroutines.core
    implementation libs.kotlin.coroutines.android

    implementation libs.androidx.multidex
    implementation libs.androidx.appcompat
    implementation libs.androidx.core.ktx
    implementation libs.androidx.splashscreen
    implementation libs.androidx.activity.ktx
    implementation libs.androidx.fragment.ktx
    implementation libs.androidx.constraintlayout
    implementation libs.androidx.viewpager
    implementation libs.androidx.viewpager2
    implementation libs.google.android.material
    implementation libs.androidx.startup.runtime
    implementation libs.androidx.swiperefreshlayout
    implementation libs.androidx.browser
    implementation libs.androidx.documentfile

    implementation libs.androidx.lifecycle.process
    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.androidx.lifecycle.common.java8
    implementation libs.androidx.lifecycle.runtime.ktx

    implementation libs.androidx.navigation.ui.ktx
    implementation libs.androidx.navigation.fragment.ktx

    implementation libs.androidx.room.runtime
    implementation libs.androidx.room.ktx
    implementation libs.androidx.room.paging
    implementation libs.androidx.room.rxjava2

    implementation libs.androidx.emoji
    implementation libs.androidx.emoji.bundled

    implementation libs.androidx.emoji2
    implementation libs.androidx.emoji2.views

    implementation libs.emoji.google
    implementation libs.emoji.google.compat

    implementation libs.androidx.work.runtime.ktx
    implementation libs.androidx.work.rxjava2

    implementation libs.androidx.paging3.runtime

    implementation libs.androidx.datastore.preferences

    implementation platform(libs.firebase.bom)
    implementation libs.firebase.analytics
    implementation libs.firebase.crashlytics
    implementation libs.firebase.config
    implementation libs.firebase.messaging
    implementation libs.firebase.perf
    implementation libs.firebase.inappmessaging.display

    implementation libs.gapo.treeview

    debugImplementation libs.gapo.flutter.debug
    releaseImplementation libs.gapo.flutter.release
    qaImplementation libs.gapo.flutter.release

    implementation libs.gms.playServices.location
    implementation libs.gms.playServices.auth
    implementation libs.gms.playServices.auth.apiPhone

    implementation libs.microsoft.msal

    implementation libs.retrofit
    implementation libs.retrofit.adapter.rxjava2
    implementation libs.retrofit.converter.moshi
    implementation libs.retrofit.converter.gson

    implementation libs.okhttp
    implementation libs.okhttp.logging.interceptor
    implementation libs.okhttp.tls

    implementation libs.mqtt

    implementation libs.tinder.scarlet
    implementation libs.tinder.scarlet.protocol.webSocketOkHttp
    implementation libs.tinder.scarlet.adapter.rx2
    implementation libs.tinder.scarlet.message.gson
    implementation libs.tinder.statemachine

    implementation libs.reactivex.rx2Java
    implementation libs.reactivex.rx2Android
    implementation libs.reactivex.rx2Kotlin
    implementation libs.reactivex.rx3Binding

    implementation libs.eventbus

    implementation libs.koin.android
    implementation libs.koin.androidx.workmanager
    implementation libs.koin.androidx.navigation

    implementation libs.sentry

    implementation libs.deeplinkdispatch
    ksp libs.deeplinkdispatch.processor

    implementation libs.glide
    implementation libs.glide.webp
    implementation libs.glide.compose

    implementation libs.autoDimens

    implementation libs.epoxy
    implementation libs.epoxy.paging3

    implementation libs.moshi.kotlin
    implementation libs.moshi.adapters

    implementation libs.kohii.core
    implementation libs.kohii.exoplayer
    implementation libs.exoplayer

    implementation libs.markwon.core
    implementation libs.markwon.linkify

    implementation libs.gson
    implementation libs.sheetmenu
    implementation libs.materialPopupMenu
    implementation libs.ucrop
    implementation libs.axrLottie
    implementation libs.timber
    implementation libs.materialprogressbar
    implementation libs.customtabshelper
    implementation libs.photoView
    implementation libs.utilcodex
    implementation(libs.imagecropview) {
        transitive = false
    }
    implementation libs.spinKit
    implementation libs.elasticviews
    implementation libs.dragToClose
    implementation libs.gpuimage
    implementation libs.google.barcode.scanning
    implementation libs.zxing.android.embedded
    implementation libs.zxing.core
    implementation(libs.swipelayout) { artifact { type = 'aar' } }
    implementation libs.xfetch2
    implementation libs.libphonenumber
    implementation libs.spyglass
    implementation libs.autocomplete
    implementation libs.joda.time
    implementation libs.webrtc
    implementation libs.balloon
    implementation libs.roundedimageview
    implementation libs.spotlight
    implementation libs.imagezoom
    implementation libs.toasty
    implementation libs.stfalconimageviewer
    implementation libs.gestureViews
    implementation libs.keyboardvisibilityevent
    implementation libs.progressbutton
    implementation libs.swipeActionView
    implementation libs.hauler
    implementation libs.compressor
    implementation libs.flexbox
    implementation libs.circleimageview
    implementation libs.audioRecordView

    implementation libs.simpleCommons
    implementation(libs.patternLockview) {
        exclude group: 'com.andrognito.patternlockview', module: 'patternlockview'
    }
    implementation(libs.rtlDuolingoViewpager) {
        exclude group: 'com.duolingo.open', module: 'rtl-viewpager'
    }

    implementation libs.rtmpRtspStreamClient
    implementation libs.jwtdecode
    implementation libs.slidableActivity
    implementation libs.newCalendarView
    implementation libs.evalExpression
    implementation libs.signaturePad
    implementation libs.dotsIndicator

    implementation libs.livekit
    implementation libs.livekitKrisp
    implementation libs.groupie
    implementation libs.groupie.viewbinding

    implementation platform(libs.androidx.compose.bom)
    implementation libs.androidx.compose.activity
    implementation libs.androidx.compose.ui
    implementation libs.androidx.compose.ui.graphics
    implementation libs.androidx.compose.ui.tooling.preview
    implementation libs.androidx.compose.material3
    debugImplementation libs.androidx.compose.ui.tooling
    debugImplementation libs.androidx.compose.ui.test.manifest

    coreLibraryDesugaring libs.desugarJdk

//    debugImplementation libs.leakcanary

    debugImplementation libs.fps.debug
    releaseImplementation libs.fps.release
    qaImplementation libs.fps.release

    testImplementation libs.junit
    testImplementation libs.androidx.test.junit
    testImplementation libs.androidx.test.core
    testImplementation libs.androidx.test.rules

    implementation libs.recaptcha
}

apply plugin: libs.plugins.gms.google.services.get().pluginId
