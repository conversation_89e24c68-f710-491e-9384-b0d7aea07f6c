# Feature Call

* [Janus](https://janus.conf.meetecho.com) là WebRTC server chịu trách nhiệm triển khai, thiết lập giao tiếp WebRTC, trao đ<PERSON><PERSON>, chuyển tiếp RTP/RTCP giữa các Peer client và logic phía Backend.
* [WebRTC](https://webrtc.org) cung cấp khả năng giao tiếp thời gian thực, hỗ trợ gửi dữ liệu audio, video và generic data.
* [WebRTC - Mozilla Docs](https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API).
* [WebRTC - SDP](https://www.tutorialspoint.com/webrtc/webrtc_session_description_protocol.htm) Là một phần quan trọng của WebRTC.
Nó là một giao thức để mô tả một đầu của kết nối hoặc kết nối tiềm năng và cách nó được cấu hình trong quá trình thương lượng giữa các Peer client về audio/video codec, thông tin mạng, địa chỉ IP và thiết bị.
Mỗi Peer client có sẵn 2 mô tả: Local Description và Remote Description.
* [WebRTC - STUN/TURN](https://www.html5rocks.com/en/tutorials/webrtc/infrastructure/).
* [WebRTC - ICE](https://developer.mozilla.org/en-US/docs/Glossary/ICE) Là một giao thức cho phép 2 Peer client tìm và thiết lập kết nối với nhau.
Nó tìm kiếm đường dẫn có độ trễ thấp nhất để kết nối ngang hàng (P2P) và thử song song các tuỳ chọn cụ thể như sau:
    * Kết nối UDP trực tiếp (chỉ trong trường hợp này máy chủ STUN được sử dụng để tìm địa chỉ mạng).
    * Kết nối TCP trực tiếp (thông qua cổng HTTP/HTTPS).
    * Kết nối gián tiếp qua máy chủ chuyển tiếp hoặc TURN server (nếu không thể kết nối trực tiếp vì firewall chặn truyền qua NAT).
* [WebRTC - Trickle ICE](https://webrtcglossary.com/trickle-ice/) Là sự tối ưu hoá của ICE cho NAT, giúp toàn bộ quá trình ICE xảy ra song song bằng cách gửi một hoặc nhiều ICE Candidate khi có sẵn mà không phải đợi một tập hợp tất cả các ứng viên giúp việc thiết lập kết nối ngang hàng nhanh chóng trong WebRTC.

### Luồng

- 1: A ấn nút Audio/Video call chuyển sang màn hình OutgoingCall => Gọi api fetch Profile của B => Gọi api fetch Janus Server information => Join và đăng ký vào Janus. Sau khi đăng ký thành công vào Janus, A gọi api Make Call tới B.

- 2: B nhận event Incoming qua MQTT hoặc FCM. Sau khi hiển thị Incoming UI thành công sẽ gọi API Ringing để thông báo cho A.
- 2.1: Nếu B ấn Decline sẽ huỷ cuộc gọi bằng cách gọi api Hangup.
- 2.2: Nếu B ấn Answer sẽ trả lời cuộc gọi và chuyển sang màn hình OutgoingCall => Gọi api fetch Profile của B => Gọi api fetch Janus Server information => Join và đăng ký vào Janus. Sau khi đăng ký thành công vào Janus, chờ Offer từ A.

- 3.1: Nếu B decline, A nhận event Decline qua MQTT hoặc FCM.
- 3.2: Nếu B answer và đã đăng ký vào Janus thành công, A sẽ nhận event CreateOffer qua MQTT hoặc FCM.
- 3.2.1: A gọi CreateOffer qua WebRTC để lấy LocalDescription gửi cho B qua Janus.
- 3.2.2: B nhận Janus Incoming event gồm SDP của A. Sau khi B SetRemoteDescription bằng SDP của A, B gọi CreateAnswer qua WebRTC để lấy LocalDescription gửi cho B qua Janus.
- 3.2.3: A nhận Janus Accept event gồm SDP của B. Sau khi A SetRemoteDescription bằng SDP của B, quá trình Trickle ICE giữa A và B xảy ra.

> Lý do tính năng Call phải sử dụng cả MQTT, FCM và Janus WebSocket vì Janus WebSocket chỉ được init khi có cuộc gọi đi/đến. Do vậy các sự kiện liên quan đến Call khi các Peer client chưa kết nối được với nhau sẽ lắng nghe qua MQTT.
Trường hợp App đang ở Foreground sẽ sử dụng FCM để backup event khi MQTT bị disconnected. Trường hợp App đang ở Background sẽ sử dụng FCM để wakeup App qua event Incoming.

![call-ice](images/call-gapo.png?raw=true)

![call-flow](images/webrtc-complete-diagram.png?raw=true)

<img src="/images/call_flow_final.png" height="1355" />

### Tích hợp

#### Tạo cuộc gọi đi

**Request Runtime Permission**

```kotlin
  permissions.launch(
      arrayOf(
          Manifest.permission.CAMERA,
          Manifest.permission.RECORD_AUDIO
      )
  )
```

**Audio Call**

```kotlin
  // DeepLink
  binding.buttonCallAudio.setOnClickListener {
         navByDeepLink(
             GapoDeepLink.Call.OutGoing(
                 GapoDeepLink.Call.OutGoing.Type.OFFER_AUDIO,
                 ChatUtils.getPartnerId(partner),
                 GapoDeepLink.Options(
                     bundle = GapoDeepLink.Call.OutGoing.createBundle(
                         partner.name,
                         partner.avatar,
                         partner.avatarThumb
                     ),
                     flags = listOf(Intent.FLAG_ACTIVITY_NO_ANIMATION to false)
                 )
             )
         )
  }
```

**Video Call**

```kotlin
  // DeepLink
  binding.buttonCallVideo.setOnClickListener {
         navByDeepLink(
             GapoDeepLink.Call.OutGoing(
                 GapoDeepLink.Call.OutGoing.Type.OFFER_VIDEO,
                 ChatUtils.getPartnerId(partner),
                 GapoDeepLink.Options(
                     bundle = GapoDeepLink.Call.OutGoing.createBundle(
                         partner.name,
                         partner.avatar,
                         partner.avatarThumb
                     ),
                     flags = listOf(Intent.FLAG_ACTIVITY_NO_ANIMATION to false)
                 )
             )
         )
  }
```

#### Observe các event liên qua đến Call

**module/build.gradle**
```gradle
implementation libs.eventbus
```

```kotlin
 override fun onCreate(savedInstanceState: Bundle?) {
      super.onCreate(savedInstanceState)
      setContentView(binding.root)

      registerEventBus()
 }

 // com.gg.gapo.core.eventbus.call.CallStickyBusEvent
 @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
 fun updateCall(event: CallStickyBusEvent) {
     if (event.callIntent != null) {
         binding.textNotice.isVisible = true
         binding.textNotice.isClickable = true
         val status = if (event.callDuration >= 0) {
             val hours = (TimeUnit.SECONDS.toHours(event.callDuration) % 60).toInt()
             val minutes = (TimeUnit.SECONDS.toMinutes(event.callDuration) % 60).toInt()
             val seconds = (TimeUnit.SECONDS.toSeconds(event.callDuration) % 60).toInt()
             if (hours > 0) {
                 String.format("%02d:%02d:%02d", hours, minutes, seconds)
             } else {
                 String.format("%02d:%02d", minutes, seconds)
             }
         } else {
             "Đang đổ chuông"
         }
         binding.textNotice.text = if (event.type == ExternalCallBusEvent.AUDIO_CALL) {
             "Quay lại cuộc gọi thoại • $status"
         } else {
             "Quay lại cuộc gọi video • $status"
         }

         binding.textNotice.setOnClickListener {
             startActivity(event.callIntent)
         }
     } else {
         binding.textNotice.isVisible = false
         binding.textNotice.isClickable = false
     }
 }

 override fun onDestroy() {
     super.onDestroy()
     unregisterEventBus()
  }
```