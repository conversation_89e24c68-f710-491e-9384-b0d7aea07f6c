package com.gg.gapo.messenger.presentation.features.create.named

import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import com.gg.gapo.analytic.features.GAPOAnalytics
import com.gg.gapo.core.eventbus.invite.InvitationCreateChatGroupSuccessBusEvent
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.assignee.picker.AssigneePickerDeepLink
import com.gg.gapo.core.navigation.deeplink.group.GroupInvitationDeepLink
import com.gg.gapo.core.navigation.deeplink.messenger.MessengerMessageDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.GapoStyles
import com.gg.gapo.core.utilities.bundle.parcelable
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.messenger.databinding.GroupNamedFragmentBinding
import com.gg.gapo.messenger.presentation.bases.BaseFragment
import com.gg.gapo.messenger.presentation.features.create.CreateConversationViewModel
import com.gg.gapo.messenger.presentation.features.create.named.adapter.GroupSelectionController
import com.gg.gapo.messenger.presentation.features.create.named.model.ListParticipantListener
import com.gg.gapo.messenger.presentation.features.create.named.model.ListParticipantViewType
import com.gg.gapo.messenger.presentation.features.create.named.viewmodel.GroupNamedViewModel
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 07/04/2021
 */
class GroupNamedFragment : BaseFragment(), ListParticipantListener {

    private var binding by autoCleared<GroupNamedFragmentBinding>()

    private val groupNamedViewModel by viewModel<GroupNamedViewModel>()
    private val createConversationViewModel by activityViewModels<CreateConversationViewModel>()

    private val groupSelectionController by lazy {
        GroupSelectionController(
            glideRequests = GapoGlide.with(this),
            context = requireContext(),
            this
        )
    }

    private val assigneePickerActivityForResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.parcelable<AssigneePickerDeepLink.Output>(AssigneePickerDeepLink.OUTPUT_EXTRA)
                    ?.let {
                        CreateNamedConversationActivity.start(
                            requireContext(),
                            Bundle().apply {
                                this.putParcelable(
                                    GroupInvitationDeepLink.GROUP_INVITATION_SELECTED,
                                    it
                                )
                            }
                        )
                    }
                activity?.finish()
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = GroupNamedFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.groupNamedViewModel = <EMAIL>
        initView()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.recyclerView.setControllerAndBuildModels(groupSelectionController)
        groupNamedViewModel.onCreateGroupSuccess.observe(viewLifecycleOwner) {
            navByDeepLink(
                MessengerMessageDeepLink(
                    conversationId = it
                )
            )
            InvitationCreateChatGroupSuccessBusEvent().postEvent()
            activity?.setResult(Activity.RESULT_OK)
            activity?.finish()
        }

        groupNamedViewModel.onInvitationFailure.observe(viewLifecycleOwner) { it ->
            it?.let {
                MaterialAlertDialogBuilder(
                    requireContext(),
                    GapoStyles.GapoDialog_Alert
                )
                    .setMessage(it)
                    .setPositiveButton(GapoStrings.shared_understood) { dialog, _ ->
                        dialog.dismiss()
                    }.show()
            }
        }
        createConversationViewModel.selectedOutput.observe(viewLifecycleOwner) { it ->
            it?.let {
                groupNamedViewModel.setOutput(it)
            }
        }

        groupNamedViewModel.threadData.observe(viewLifecycleOwner) {
            groupSelectionController.setData(ArrayList(it), ListParticipantViewType.GROUP_CHAT)
        }
        groupNamedViewModel.departmentData.observe(viewLifecycleOwner) {
            groupSelectionController.setData(ArrayList(it), ListParticipantViewType.DEPARTMENT)
        }
        groupNamedViewModel.roleData.observe(viewLifecycleOwner) {
            groupSelectionController.setData(ArrayList(it), ListParticipantViewType.ROLE)
        }
        groupNamedViewModel.botData.observe(viewLifecycleOwner) {
            groupSelectionController.setData(ArrayList(it), ListParticipantViewType.BOT)
        }
        groupNamedViewModel.usersData.observe(viewLifecycleOwner) {
            groupSelectionController.setData(ArrayList(it), ListParticipantViewType.USER)
        }

        binding.imageBack.setOnClickListener {
            activity?.finish()
        }
        binding.buttonContinueLarge.setDebouncedClickListener {
            groupNamedViewModel.createGroup()
        }
        binding.textContinue.setDebouncedClickListener {
            assigneePickerActivityForResultLauncher.launch(
                AssigneePickerDeepLink(
                    options = GapoDeepLink.Options(
                        bundle = bundleOf(
                            AssigneePickerDeepLink.INPUT_EXTRA to AssigneePickerDeepLink.Input
                                .Builder()
                                .setTitle(getString(GapoStrings.messenger_create_group))
                                .setActionButtonTitle(getString(GapoStrings.shared_continue))
                                .setMode(AssigneePickerDeepLink.Input.Mode.EDIT)
                                .setTabs(
                                    arrayListOf(
                                        AssigneePickerDeepLink.Input.Tab.MEMBER,
                                        AssigneePickerDeepLink.Input.Tab.GROUP,
                                        AssigneePickerDeepLink.Input.Tab.DEPARTMENT,
                                        AssigneePickerDeepLink.Input.Tab.ROLE,
                                        AssigneePickerDeepLink.Input.Tab.BOT
                                    )
                                )
                                .setSelectedMemberIds(
                                    groupNamedViewModel.usersData.value.orEmpty().map { it.id }
                                )
                                .setSelectedThreadIds(
                                    groupNamedViewModel.threadData.value.orEmpty().map { it.id }
                                )
                                .setSelectedDepartmentIds(
                                    groupNamedViewModel.departmentData.value.orEmpty()
                                        .map { it.id }
                                )
                                .setSelectedRoleIds(
                                    groupNamedViewModel.roleData.value.orEmpty().map { it.id }
                                )
                                .setSelectedBotIds(
                                    groupNamedViewModel.botData.value.orEmpty().map { it.id }
                                )
                                .setSConfigs(
                                    AssigneePickerDeepLink.Input.SConfigs(
                                        showBtnBack = true,
                                        showBtnDone = true,
                                        showBtnViewSelectedMembers = false,
                                        searchOnlyCurrentWorkspace = true,
                                        limitUsers = null
                                    )
                                )
                                .build()
                        )
                    )
                ).getIntent(requireContext())
            )
        }
    }

    override fun trackingScreenName() {
        activity?.let {
            GAPOAnalytics.getInstance(it).logScreenGroupNamed(it)
        }
    }

    private fun initView() {
        requireActivity().intent?.extras?.parcelable<AssigneePickerDeepLink.Output>(
            GroupInvitationDeepLink.GROUP_INVITATION_SELECTED
        )?.let {
            createConversationViewModel.setData(it)
        }
    }

    override fun onDeleteItem(type: ListParticipantViewType, id: String) {
        groupNamedViewModel.delete(id = id, type = type)
    }

    override fun onLoadMore(type: ListParticipantViewType) {
    }
}
