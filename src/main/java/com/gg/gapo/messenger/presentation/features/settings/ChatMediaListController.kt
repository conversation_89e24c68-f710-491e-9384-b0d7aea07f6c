package com.gg.gapo.messenger.presentation.features.settings

import android.content.Context
import androidx.core.content.ContextCompat
import androidx.core.text.toSpannable
import com.airbnb.epoxy.AsyncEpoxyController
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.domain.models.Message
import com.gg.gapo.messenger.helper.utils.CommonUtils
import com.gg.gapo.messenger.presentation.features.interfaces.MediaMessageListener
import com.gg.gapo.messenger.presentation.features.settings.models.*
import java.text.DateFormat
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList

class ChatMediaListController(
    private val context: Context,
    private val type: MediaListType = MediaListType.LINKS,
    private val mediaListener: MediaMessageListener? = null,
    private val isFeatureDownloadEnabled: Boolean = true,
    private val emptyListListener: ChatSettingEmptyListListener? = null
) : AsyncEpoxyController() {

    private val data = CopyOnWriteArrayList<Message>()

    private val ID_EMPTY = System.currentTimeMillis()
    private var showEmpty = false

    init {
        setFilterDuplicates(true)
    }

    override fun buildModels() {
        when (type) {
            MediaListType.FILES -> {
                filesModel()
            }
            MediaListType.LINKS -> {
                linksModel()
            }
            MediaListType.IMAGE -> {
                imagesModel()
            }
            MediaListType.VIDEO -> {
                videosModel()
            }
        }
        if (data.isEmpty() && showEmpty) {
            ChatSettingEmptyListModel_().id(ID_EMPTY)
                .listener(emptyListListener)
                .addTo(this)
        }
    }

    private fun filesModel() {
        data.sortedByDescending { it.createdAt }.forEachIndexed { index, message ->
            if (index == 0 || index > 0 && shouldShowTimeHeader(
                    message.createdAt,
                    data[index - 1].createdAt
                )
            ) {
                GroupChatSettingsItemHeaderSectionModel_()
                    .id(index)
                    .title(
                        DateFormat.getDateInstance(DateFormat.LONG).format(Date(message.createdAt))
                    )
                    .textAllCaps(false)
                    .textColorRes(ContextCompat.getColor(context, R.color.color_4D4D4D))
                    .textFontSize(14f)
                    .viewPaddingStart(16)
                    .viewPaddingTop(16)
                    .viewPaddingBottom(0)
                    .viewPaddingEnd(0)
                    .addTo(this)
            }
            ChatFileInfoItemModel_()
                .id(message.id)
                .data(message)
                .downloadable(isFeatureDownloadEnabled)
                .content(message?.body?.metadata?.fileInformation?.firstOrNull()?.name.orEmpty().toSpannable())
                .mediaListener(mediaListener)
                .addTo(this)
        }
    }

    private fun videosModel() {
        data.sortedByDescending { it.createdAt }.forEachIndexed { index, message ->
            if (index == 0 || index > 0 && shouldShowTimeHeader(
                    message.createdAt,
                    data[index - 1].createdAt
                )
            ) {
                GroupChatSettingsItemHeaderSectionModel_()
                    .id(index)
                    .title(
                        DateFormat.getDateInstance(DateFormat.LONG).format(Date(message.createdAt))
                    )
                    .textAllCaps(false)
                    .textColorRes(ContextCompat.getColor(context, R.color.color_4D4D4D))
                    .textFontSize(14f)
                    .viewPaddingStart(12)
                    .viewPaddingTop(16)
                    .viewPaddingBottom(8)
                    .viewPaddingEnd(0)
                    .spanSizeOverride { _, _, _ -> 3 }
                    .addTo(this)
            }
            ChatVideoGridItemModel_()
                .id(message.id)
                .data(message)
                .mediaListener(mediaListener)
                .addTo(this)
        }
    }

    private fun imagesModel() {
        data.sortedByDescending { it.createdAt }.forEachIndexed { index, message ->
            if (index == 0 || index > 0 && shouldShowTimeHeader(
                    message.createdAt,
                    data[index - 1].createdAt
                )
            ) {
                GroupChatSettingsItemHeaderSectionModel_()
                    .id(index)
                    .title(
                        DateFormat.getDateInstance(DateFormat.LONG).format(Date(message.createdAt))
                    )
                    .textAllCaps(false)
                    .textColorRes(ContextCompat.getColor(context, R.color.color_4D4D4D))
                    .textFontSize(14f)
                    .viewPaddingStart(12)
                    .viewPaddingTop(16)
                    .viewPaddingBottom(8)
                    .viewPaddingEnd(0)
                    .spanSizeOverride { _, _, _ -> 3 }
                    .addTo(this)
            }
            ChatImageGridItemModel_()
                .id("${message.id}_${message.clientId}")
                .data(message)
                .mediaListener(mediaListener)
                .addTo(this)
        }
    }

    private fun linksModel() {
        data.sortedByDescending { it.createdAt }.forEachIndexed { index, message ->
            if (index == 0 || index > 0 && shouldShowTimeHeader(
                    message.createdAt,
                    data[index - 1].createdAt
                )
            ) {
                GroupChatSettingsItemHeaderSectionModel_()
                    .id(index)
                    .title(
                        DateFormat.getDateInstance(DateFormat.LONG).format(Date(message.createdAt))
                    )
                    .textAllCaps(false)
                    .textColorRes(ContextCompat.getColor(context, R.color.color_4D4D4D))
                    .textFontSize(14f)
                    .viewPaddingStart(16)
                    .viewPaddingTop(16)
                    .viewPaddingBottom(0)
                    .viewPaddingEnd(0)
                    .addTo(this)
            }
            ChatLinkInfoItemModel_()
                .id("${message.id}_$index")
                .data(message)
                .mediaListener(mediaListener)
                .addTo(this)
        }
    }

    private fun shouldShowTimeHeader(
        messageCreatedAt: Long,
        previousMessageCreatedAt: Long
    ): Boolean {
        val nextTimeLabel = CommonUtils.format(messageCreatedAt, "dd/MM/yyyy")
        val timeLabel = CommonUtils.format(previousMessageCreatedAt, "dd/MM/yyyy")
        return timeLabel != nextTimeLabel
    }

    fun getLastId() = data.lastOrNull()?.id?.toIntOrNull() ?: 0

    fun addAll(list: List<Message>) {
        var shouldUpdate = false
        list.forEach { item ->
            val index = data.indexOfFirst { it.id == item.id && it.clientId == item.clientId }
            if (index == -1) {
                data.add(item)
                shouldUpdate = true
            }
        }
        if (list.isEmpty() && data.isEmpty()) {
            shouldUpdate = true
            showEmpty = true
        }
        if (shouldUpdate) {
            requestModelBuild()
        }
    }

    fun addAllAbsent(list: List<Message>) {
        list.forEach { item ->
            val index = data.indexOfFirst { it.id == item.id && it.clientId == item.clientId }
            if (index == -1) {
                data.add(item)
            }
        }
        if (list.isEmpty() && data.isEmpty()) {
            showEmpty = true
        }
        requestModelBuild()
    }

    fun clearAll() {
        data.clear()
        requestModelBuild()
    }

    fun size() = data.size
}

enum class MediaListType {
    FILES, LINKS, IMAGE, VIDEO
}
