package com.gg.gapo.messenger.presentation.common.components

import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import com.google.android.material.imageview.ShapeableImageView

class SquareImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ShapeableImageView(context, attrs, defStyleAttr) {

    private val gestureDetector: GestureDetector
    private var listener: SquareOnTouchListener? = null

    fun addListener(listener: SquareOnTouchListener) {
        this.listener = listener
    }

    init {
        gestureDetector = GestureDetector(
            context,
            object : GestureDetector.SimpleOnGestureListener() {
                override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                    listener?.onSingleTap(e)
                    return false
                }

                override fun onDoubleTap(e: MotionEvent): Boolean {
                    listener?.onDoubleTap(e)
                    return false
                }

                override fun onLongPress(e: MotionEvent) {
                    listener?.onLongPress(e)
                }
            }
        )

        setOnTouchListener(object : OnTouchListener {
            override fun onTouch(v: View?, event: MotionEvent): Boolean {
                if (gestureDetector.onTouchEvent(event)) {
                    return false
                }
                return true
            }
        })
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, widthMeasureSpec)
    }

    interface SquareOnTouchListener {

        fun onLongPress(e: MotionEvent)

        fun onSingleTap(e: MotionEvent)

        fun onDoubleTap(e: MotionEvent)
    }
}
