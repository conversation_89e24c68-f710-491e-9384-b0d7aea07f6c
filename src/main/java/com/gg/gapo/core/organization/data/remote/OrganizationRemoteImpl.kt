package com.gg.gapo.core.organization.data.remote

import com.gg.gapo.core.organization.data.remote.api.OrganizationApiService
import com.gg.gapo.core.organization.data.remote.model.OrganizationFetchTreesResponse

/**
 * <AUTHOR>
 * @since 10/10/22
 */
internal class OrganizationRemoteImpl(private val organizationApiService: OrganizationApiService) :
    OrganizationRemote {
    override suspend fun fetchTrees(): OrganizationFetchTreesResponse {
        return organizationApiService.fetchTrees()
    }
}
