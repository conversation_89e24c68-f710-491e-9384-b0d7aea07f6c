package com.gg.gapo.core.feed.usecase.story.data.remote.model.response

import com.gg.gapo.core.feed.usecase.story.data.remote.model.FeedFriendStoryDto
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @since 04/05/2021
 */
internal data class FetchFriendStoriesResponse(
    @SerializedName("data") val friendStories: List<FeedFriendStoryDto>?,
    @SerializedName("links") val links: Links?
) : FetchStoryBaseResponse() {

    data class Links(@SerializedName("next") val next: String?) {

        val nextQueries: Map<String, String>
            get() {
                return try {
                    val listQuery = next?.split("&") ?: return emptyMap()
                    val queries = mutableMapOf<String, String>()
                    listQuery.forEach { child ->
                        val (key, value) = child.split("=")
                        queries[key] = value
                    }
                    queries
                } catch (e: Exception) {
                    emptyMap()
                }
            }
    }
}
