package com.gg.gapo.core.feed.item

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import com.gg.gapo.core.feed.R
import com.gg.gapo.core.feed.databinding.*
import com.gg.gapo.core.feed.item.achievement.viewholder.FeedAchievementPostsListViewHolder
import com.gg.gapo.core.feed.item.announcement.viewholder.FeedAnnouncementPostsListViewHolder
import com.gg.gapo.core.feed.item.compose.viewholder.FeedComposeViewHolder
import com.gg.gapo.core.feed.item.empty.viewholder.FeedEmptyViewHolder
import com.gg.gapo.core.feed.item.findfriend.viewholder.FeedFindFriendViaContactViewHolder
import com.gg.gapo.core.feed.item.group.details.header.viewholder.FeedGroupDetailsHeaderViewHolder
import com.gg.gapo.core.feed.item.group.details.pinnedrow.viewholder.FeedGroupDetailsPinnedPostRowViewHolder
import com.gg.gapo.core.feed.item.group.mine.viewholder.FeedGroupMyGroupEmptyViewHolder
import com.gg.gapo.core.feed.item.group.mine.viewholder.FeedGroupMyGroupsListViewHolder
import com.gg.gapo.core.feed.item.group.rules.viewholder.FeedGroupRulesListViewHolder
import com.gg.gapo.core.feed.item.group.schedule.viewholder.FeedGroupScheduleViewHolder
import com.gg.gapo.core.feed.item.onboarding.viewholder.FeedOnboardingsListViewHolder
import com.gg.gapo.core.feed.item.placeholder.viewholder.FeedPlaceholderViewHolder
import com.gg.gapo.core.feed.item.post.achievement.viewholder.FeedPostAchievementViewHolder
import com.gg.gapo.core.feed.item.post.announcement.summary.viewholder.FeedPostAnnouncementSummaryViewHolder
import com.gg.gapo.core.feed.item.post.askme.viewholder.FeedPostLetAskMeViewHolder
import com.gg.gapo.core.feed.item.post.attachment.viewholder.FeedPostAttachmentsListViewHolder
import com.gg.gapo.core.feed.item.post.background.viewholder.FeedPostBackgroundViewHolder
import com.gg.gapo.core.feed.item.post.change.avatar.viewholder.FeedPostChangeAvatarViewHolder
import com.gg.gapo.core.feed.item.post.comment.viewholder.FeedPostCommentContentViewHolder
import com.gg.gapo.core.feed.item.post.comment.viewholder.FeedPostCommentShortRepliesViewHolder
import com.gg.gapo.core.feed.item.post.error.post.viewholder.FeedPostErrorViewHolder
import com.gg.gapo.core.feed.item.post.error.share.viewholder.FeedPostSharedErrorViewHolder
import com.gg.gapo.core.feed.item.post.filter.viewholder.FeedFilterPostsListViewHolder
import com.gg.gapo.core.feed.item.post.footer.viewholder.FeedChildPostFooterViewHolder
import com.gg.gapo.core.feed.item.post.footer.viewholder.FeedPostCommentBlockedViewHolder
import com.gg.gapo.core.feed.item.post.footer.viewholder.FeedPostFooterViewHolder
import com.gg.gapo.core.feed.item.post.group.pending.viewholder.FeedPostGroupPendingActionViewHolder
import com.gg.gapo.core.feed.item.post.header.viewholder.FeedCombinePostHeaderViewHolder
import com.gg.gapo.core.feed.item.post.header.viewholder.FeedPostHeaderViewHolder
import com.gg.gapo.core.feed.item.post.input.viewholder.FeedPostInputCommentViewHolder
import com.gg.gapo.core.feed.item.post.media.viewholder.FeedPostMediaListViewHolder
import com.gg.gapo.core.feed.item.post.more.viewholder.FeedCombinePostViewMoreViewHolder
import com.gg.gapo.core.feed.item.post.placeholder.viewholder.FeedCombinePostPlaceHolderViewHolder
import com.gg.gapo.core.feed.item.post.pollvote.viewholder.FeedPostPollVoteListViewHolder
import com.gg.gapo.core.feed.item.post.previewlink.viewholder.FeedPostPreviewLinkViewHolder
import com.gg.gapo.core.feed.item.post.question.viewholder.FeedPostQuestionViewHolder
import com.gg.gapo.core.feed.item.post.react.viewholder.FeedPostReactViewHolder
import com.gg.gapo.core.feed.item.post.text.viewholder.FeedPostTextViewHolder
import com.gg.gapo.core.feed.item.reachedend.viewholder.FeedReachedEndViewHolder
import com.gg.gapo.core.feed.item.row.viewholder.FeedTertiaryRowViewHolder
import com.gg.gapo.core.feed.item.shimmer.viewholder.FeedPostShimmerViewHolder
import com.gg.gapo.core.feed.item.story.viewholder.FeedStoriesListViewHolder
import com.gg.gapo.core.feed.item.suggestion.group.viewholder.FeedSuggestedGroupsListViewHolder
import com.gg.gapo.core.feed.item.uploading.viewholder.FeedPostUploadingViewHolder
import com.gg.gapo.core.feed.item.user.achievement.viewholder.FeedUserProfileAchievementsListViewHolder
import com.gg.gapo.core.feed.item.user.album.viewholder.FeedUserProfilePhotoAlbumListViewHolder
import com.gg.gapo.core.feed.item.user.follow.viewholder.FeedUserProfileFollowListViewHolder
import com.gg.gapo.core.feed.item.user.friend.viewholder.FeedUserProfileFriendsListViewHolder
import com.gg.gapo.core.feed.item.user.profile.viewholder.FeedUserProfileViewHolder
import com.gg.gapo.core.feed.item.user.row.viewholder.FeedUserProfileAskAchievementRowViewHolder
import com.gg.gapo.core.feed.utils.FeedImageLoader
import com.gg.gapo.core.feed.utils.FeedVideoPlayer
import com.gg.gapo.core.utilities.view.recyclerview.NoLimitRecycledViewPool
import kotlinx.coroutines.CoroutineDispatcher

/**
 * <AUTHOR>
 * @since 02/04/2021
 */
internal class FeedViewHolderFactory(
    context: Context,
    private val nestedRecyclerViewViewPool: NoLimitRecycledViewPool,
    private val feedVideoPlayer: FeedVideoPlayer?,
    private val feedImageLoader: FeedImageLoader,
    private val differCoroutineDispatcher: CoroutineDispatcher
) {

    private val layoutInflater = LayoutInflater.from(context)

    fun create(parent: ViewGroup, viewType: Int): FeedViewHolder<*, *> {
        return when (viewType) {
            R.layout.feed_compose_item -> FeedComposeViewHolder(
                FeedComposeItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_stories_list_item -> FeedStoriesListViewHolder(
                FeedStoriesListItemBinding.inflate(layoutInflater, parent, false),
                nestedRecyclerViewViewPool,
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_onboardings_list_item -> FeedOnboardingsListViewHolder(
                FeedOnboardingsListItemBinding.inflate(layoutInflater, parent, false),
                nestedRecyclerViewViewPool,
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_suggested_groups_list_item -> FeedSuggestedGroupsListViewHolder(
                FeedSuggestedGroupsListItemBinding.inflate(layoutInflater, parent, false),
                nestedRecyclerViewViewPool,
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_find_friend_via_contact_item -> FeedFindFriendViaContactViewHolder(
                FeedFindFriendViaContactItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_post_header_item -> FeedPostHeaderViewHolder(
                FeedPostHeaderItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_combine_post_header_item -> FeedCombinePostHeaderViewHolder(
                FeedCombinePostHeaderItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_post_uploading_item -> FeedPostUploadingViewHolder(
                FeedPostUploadingItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_post_text_item -> FeedPostTextViewHolder(
                FeedPostTextItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_post_question_item -> FeedPostQuestionViewHolder(
                FeedPostQuestionItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_post_media_list_item -> FeedPostMediaListViewHolder(
                FeedPostMediaListItemBinding.inflate(layoutInflater, parent, false),
                feedVideoPlayer,
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_post_change_avatar_item -> FeedPostChangeAvatarViewHolder(
                FeedPostChangeAvatarItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_post_preview_link_item -> FeedPostPreviewLinkViewHolder(
                FeedPostPreviewLinkItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_post_let_ask_me_item -> FeedPostLetAskMeViewHolder(
                FeedPostLetAskMeItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_post_achievement_item -> FeedPostAchievementViewHolder(
                FeedPostAchievementItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_post_attachments_list_item -> FeedPostAttachmentsListViewHolder(
                FeedPostAttachmentsListItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_post_poll_vote_list_item -> FeedPostPollVoteListViewHolder(
                FeedPostPollVoteListItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_post_post_background_item -> FeedPostBackgroundViewHolder(
                FeedPostPostBackgroundItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_post_shared_error_item -> FeedPostSharedErrorViewHolder(
                FeedPostSharedErrorItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_post_react_item -> FeedPostReactViewHolder(
                FeedPostReactItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_post_comment_blocked_layout -> FeedPostCommentBlockedViewHolder(
                FeedPostCommentBlockedLayoutBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_post_comment_content_item -> FeedPostCommentContentViewHolder(
                FeedPostCommentContentItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader,
                feedVideoPlayer
            )
            R.layout.feed_post_comment_short_replies_item -> FeedPostCommentShortRepliesViewHolder(
                FeedPostCommentShortRepliesItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_post_input_comment_item -> FeedPostInputCommentViewHolder(
                FeedPostInputCommentItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_post_group_pending_action_item -> FeedPostGroupPendingActionViewHolder(
                FeedPostGroupPendingActionItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_post_announcement_summary_item -> FeedPostAnnouncementSummaryViewHolder(
                FeedPostAnnouncementSummaryItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_post_footer_item -> FeedPostFooterViewHolder(
                FeedPostFooterItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_post_shimmer_item -> FeedPostShimmerViewHolder(
                FeedPostShimmerItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_group_my_groups_list_item -> FeedGroupMyGroupsListViewHolder(
                FeedGroupMyGroupsListItemBinding.inflate(layoutInflater, parent, false),
                nestedRecyclerViewViewPool,
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_group_my_group_empty_item -> FeedGroupMyGroupEmptyViewHolder(
                FeedGroupMyGroupEmptyItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_group_details_header_item -> FeedGroupDetailsHeaderViewHolder(
                FeedGroupDetailsHeaderItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_group_schedule_item -> FeedGroupScheduleViewHolder(
                FeedGroupScheduleItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_group_details_pinned_post_row_item -> FeedGroupDetailsPinnedPostRowViewHolder(
                FeedGroupDetailsPinnedPostRowItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_group_rules_list_item -> FeedGroupRulesListViewHolder(
                FeedGroupRulesListItemBinding.inflate(layoutInflater, parent, false),
                differCoroutineDispatcher
            )
            R.layout.feed_user_profile_item -> FeedUserProfileViewHolder(
                FeedUserProfileItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_user_profile_photo_albums_list_item -> FeedUserProfilePhotoAlbumListViewHolder(
                FeedUserProfilePhotoAlbumsListItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_user_profile_friends_list_item -> FeedUserProfileFriendsListViewHolder(
                FeedUserProfileFriendsListItemBinding.inflate(layoutInflater, parent, false),
                nestedRecyclerViewViewPool,
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_user_profile_following_list_item -> FeedUserProfileFollowListViewHolder(
                FeedUserProfileFollowingListItemBinding.inflate(layoutInflater, parent, false),
                nestedRecyclerViewViewPool,
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_user_profile_achievements_list_item -> FeedUserProfileAchievementsListViewHolder(
                FeedUserProfileAchievementsListItemBinding.inflate(layoutInflater, parent, false),
                nestedRecyclerViewViewPool,
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_user_profile_ask_achievement_row_item -> FeedUserProfileAskAchievementRowViewHolder(
                FeedUserProfileAskAchievementRowItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_empty_item -> FeedEmptyViewHolder(
                FeedEmptyItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_reached_end_item -> FeedReachedEndViewHolder(
                FeedReachedEndItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_tertiary_row_item -> FeedTertiaryRowViewHolder(
                FeedTertiaryRowItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_placeholder_item -> FeedPlaceholderViewHolder(
                FeedPlaceholderItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_announcement_posts_list_item -> FeedAnnouncementPostsListViewHolder(
                FeedAnnouncementPostsListItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader,
                differCoroutineDispatcher
            )
            R.layout.feed_post_error_item -> FeedPostErrorViewHolder(
                FeedPostErrorItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_combine_post_view_more_item -> FeedCombinePostViewMoreViewHolder(
                FeedCombinePostViewMoreItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_child_post_footer_item -> FeedChildPostFooterViewHolder(
                FeedChildPostFooterItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_combine_post_place_holder_item -> FeedCombinePostPlaceHolderViewHolder(
                FeedCombinePostPlaceHolderItemBinding.inflate(layoutInflater, parent, false)
            )
            R.layout.feed_achievement_posts_list_item -> FeedAchievementPostsListViewHolder(
                FeedAchievementPostsListItemBinding.inflate(layoutInflater, parent, false),
                feedImageLoader
            )
            R.layout.feed_posts_list_filter_item -> FeedFilterPostsListViewHolder(
                FeedPostsListFilterItemBinding.inflate(layoutInflater, parent, false)
            )

            else -> throw IllegalArgumentException()
        }
    }
}
