package com.gg.gapo.core.feed.common.drawable

import android.graphics.Canvas
import android.graphics.ColorFilter
import android.graphics.drawable.Drawable

/**
 * <AUTHOR>
 * @since 30/09/2021
 */
internal class FeedGravityCompoundDrawable(
    private val drawable: Drawable
) : Drawable() {

    override fun getIntrinsicWidth(): Int {
        return drawable.intrinsicWidth
    }

    override fun getIntrinsicHeight(): Int {
        return drawable.intrinsicHeight
    }

    override fun draw(canvas: Canvas) {
        val halfCanvas = canvas.height / 2
        val halfDrawable = drawable.intrinsicHeight / 2

        // align to top
        canvas.save()
        canvas.translate(0f, (-halfCanvas + halfDrawable).toFloat())
        drawable.draw(canvas)
        canvas.restore()
    }

    override fun setAlpha(alpha: Int) {
        drawable.alpha = alpha
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        drawable.colorFilter = colorFilter
    }

    override fun getOpacity(): Int {
        return drawable.opacity
    }
}
