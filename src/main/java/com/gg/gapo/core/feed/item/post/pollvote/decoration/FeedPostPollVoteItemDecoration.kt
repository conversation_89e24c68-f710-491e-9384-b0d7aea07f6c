package com.gg.gapo.core.feed.item.post.pollvote.decoration

import android.content.Context
import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.gg.gapo.core.ui.GapoAutoDimens

/**
 * <AUTHOR>
 * @since 11/08/2021
 */
internal class FeedPostPollVoteItemDecoration(
    context: Context
) : RecyclerView.ItemDecoration() {

    private val space8dp = context.resources.getDimensionPixelSize(GapoAutoDimens._8dp)

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)
        if (position != RecyclerView.NO_POSITION) {
            outRect.top = if (position != 0) {
                space8dp
            } else {
                0
            }
        }
    }
}
