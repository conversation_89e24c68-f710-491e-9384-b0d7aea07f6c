package com.gg.gapo.core.feed.item.user.album.viewholder

import android.os.Bundle
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.gg.gapo.core.feed.databinding.FeedUserProfilePhotoAlbumItemBinding
import com.gg.gapo.core.feed.item.FeedViewHolder
import com.gg.gapo.core.feed.item.user.album.model.FeedUserProfilePhotoAlbumViewData
import com.gg.gapo.core.feed.utils.FeedImageLoader
import com.gg.gapo.core.feed.utils.removeOnClickListener
import com.gg.gapo.core.utilities.view.setDebouncedClickListener

/**
 * <AUTHOR>
 * @since 24/05/2021
 */
internal class FeedUserProfilePhotoAlbumViewHolder(
    private val binding: FeedUserProfilePhotoAlbumItemBinding,
    private val feedImageLoader: FeedImageLoader
) : FeedViewHolder<FeedUserProfilePhotoAlbumItemBinding, FeedUserProfilePhotoAlbumViewData>(binding) {

    override fun onBind(item: FeedUserProfilePhotoAlbumViewData, bundle: Bundle?) {
        binding.layoutRoot.setDebouncedClickListener {
            item.interactor.onUserProfilePhotoAlbumClickOnPhoto(bindingAdapterPosition)
        }
        feedImageLoader.asBitmap()
            .load(item.photo)
            .thumbnail(feedImageLoader.asBitmap().sizeMultiplier(0.25f))
            .dontAnimate()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .priority(Priority.IMMEDIATE)
            .into(binding.imagePhoto)
    }

    override fun onViewRecycled() {
        feedImageLoader.clear(binding.imagePhoto)
        binding.layoutRoot.removeOnClickListener()
    }
}
