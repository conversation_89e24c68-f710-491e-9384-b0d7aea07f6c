package com.gg.gapo.core.feed.item.group.details.pinnedrow.model

import android.content.Context
import android.text.Spanned
import android.text.SpannedString
import androidx.core.os.bundleOf
import com.gg.gapo.core.feed.R
import com.gg.gapo.core.feed.item.FeedViewData
import com.gg.gapo.core.feed.item.group.details.pinnedrow.interactor.FeedGroupDetailsPinnedPostRowInteractor
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.utilities.resources.GapoGlobalResources

/**
 * <AUTHOR>
 * @since 15/04/2021
 */
data class FeedGroupDetailsPinnedPostRowViewData(
    override val itemId: String,
    override val interactor: FeedGroupDetailsPinnedPostRowInteractor,
    val groupId: String,
    var total: Total
) : FeedViewData {

    override val layoutRes: Int
        get() = R.layout.feed_group_details_pinned_post_row_item

    override fun equals(other: Any?): Boolean {
        return if (other !is FeedGroupDetailsPinnedPostRowViewData) false
        else areItemsTheSame(other) && areContentsTheSame(other)
    }

    override fun hashCode(): Int {
        return itemId.hashCode() + groupId.hashCode() + total.hashCode()
    }

    override fun shallowCopy(): FeedGroupDetailsPinnedPostRowViewData = copy(
        interactor = interactor,
        total = total.copy(spanned = total.spanned)
    )

    override fun areItemsTheSame(item: FeedViewData): Boolean {
        return if (item !is FeedGroupDetailsPinnedPostRowViewData) false
        else item.itemId == itemId && item.groupId == groupId
    }

    override fun areContentsTheSame(item: FeedViewData): Boolean {
        return if (item !is FeedGroupDetailsPinnedPostRowViewData) false
        else item.total == total
    }

    override fun getChangePayload(item: FeedViewData) = bundleOf(TOTAL_CHANGED_EXTRA to true)

    data class Total internal constructor(val total: Int, val spanned: Spanned) {

        override fun equals(other: Any?): Boolean {
            return if (other !is Total) false
            else other.total == total
        }

        override fun hashCode(): Int {
            return total
        }

        companion object {
            fun create(context: Context, total: Int): Total {
                return Total(
                    total,
                    SpannedString(
                        GapoGlobalResources.getString(
                            GapoStrings.feeds_group_details_pinned_post_row_view_all_format,
                            total
                        )
                    )
                )
            }
        }
    }

    companion object {

        internal const val TOTAL_CHANGED_EXTRA = "TOTAL_CHANGED_EXTRA"

        const val DEFAULT_ITEM_ID = "GROUP_DETAILS_PINNED_POST_ROW_ITEM_ID"
    }
}
