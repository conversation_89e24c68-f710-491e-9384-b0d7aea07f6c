package com.gg.gapo.core.feed.item.user.follow.model

import android.content.Context
import android.os.Bundle
import android.text.Spanned
import android.text.SpannedString
import com.gg.gapo.core.feed.R
import com.gg.gapo.core.feed.item.FeedViewData
import com.gg.gapo.core.feed.item.user.follow.interactor.FeedUserFollowInteractor
import com.gg.gapo.core.ui.GapoStrings

/**
 * <AUTHOR>
 * @since 15/05/2024
 */
data class FeedUserProfileFollowListViewData(
    override val itemId: String,
    override val interactor: FeedUserFollowInteractor,
    val data: MutableList<FeedUserProfileFollowViewData>,
    val total: Total
) : FeedViewData {

    override val layoutRes: Int
        get() = R.layout.feed_user_profile_following_list_item

    override fun equals(other: Any?): Boolean {
        return if (other !is FeedUserProfileFollowListViewData) false
        else areItemsTheSame(other) && areContentsTheSame(other)
    }

    override fun hashCode(): Int {
        return itemId.hashCode() + data.hashCode() + total.hashCode()
    }

    override fun shallowCopy(): FeedUserProfileFollowListViewData = copy(
        interactor = interactor,
        data = data.map { it.shallowCopy() }.toMutableList(),
        total = total.copy(spanned = total.spanned)
    )

    override fun areItemsTheSame(item: FeedViewData): Boolean {
        return if (item !is FeedUserProfileFollowListViewData) false
        else item.itemId == itemId
    }

    override fun areContentsTheSame(item: FeedViewData): Boolean {
        return if (item !is FeedUserProfileFollowListViewData) false
        else item.data != data && item.total == total
    }

    override fun getChangePayload(item: FeedViewData): Bundle {
        val bundle = Bundle()
        if (item is FeedUserProfileFollowListViewData) {
            if (item.total != total) {
                bundle.putBoolean(TOTAL_CHANGED_EXTRA, true)
            }
        }
        return bundle
    }

    data class Total internal constructor(val total: Int, val spanned: Spanned) {

        val isNotEmpty: Boolean
            get() = total > 0

        override fun equals(other: Any?): Boolean {
            return if (other !is Total) false
            else other.total == total
        }

        override fun hashCode(): Int {
            return total
        }

        companion object {
            fun create(context: Context, total: Int): Total {
                return Total(
                    total,
                    SpannedString(
                        "$total ${context.getString(GapoStrings.livestream_message_many_follower)}"
                    )
                )
            }
        }
    }

    companion object {
        const val DEFAULT_ITEM_ID = "USER_MY_FOLLOW_LIST_ITEM_ID"

        internal const val TOTAL_CHANGED_EXTRA = "TOTAL_CHANGED_EXTRA"
    }
}
