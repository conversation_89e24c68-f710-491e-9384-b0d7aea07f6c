package com.gg.gapo.core.feed.item.post.media.interactor

import com.gg.gapo.core.feed.item.post.FeedPostInteractor

/**
 * <AUTHOR>
 * @since 26/04/2021
 */
interface FeedPostMediaVideoInteractor : FeedPostInteractor {

    /**
     * callback trả về tổng thời gian đã xem video
     * [com.gg.gapo.core.feed.item.post.media.viewholder.FeedPostMediaVideoViewHolder]
     * [com.gg.gapo.core.feed.tracker.FeedVideoProgressTracker]
     * @param postId
     * @param videoId
     * @param watchedLength tổng thời gian đã xem video tính từ thời điểm start đến khi video bị stop hoặc player bị release.
     * @param duration Duration của video.
     */
    fun onPostMediaVideoWatchedLength(postId: String, parentPostId: String?, mediaId: String, watchedLength: Long, duration: Long) {}

    /**
     * callback khi ấn vào Video trên post.
     * [com.gg.gapo.core.feed.item.post.media.viewholder.FeedPostMediaVideoViewHolder]
     * @param postId
     * @param position
     */
    fun onPostMediaVideoClickOnVideo(postId: String, parentPostId: String?, mediaId: String, position: Int) {}

    /**
     * callback khi ấn vào LiveStream trên post.
     * [com.gg.gapo.core.feed.item.post.media.viewholder.FeedPostMediaVideoViewHolder]
     * @param postId
     * @param position
     */
    fun onPostMediaVideoClickOnLiveStream(postId: String, parentPostId: String?, mediaId: String, position: Int) {}

    fun onPostMediaVideoAddViewCount(postId: String, parentPostId: String?, mediaId: String) {}
}
