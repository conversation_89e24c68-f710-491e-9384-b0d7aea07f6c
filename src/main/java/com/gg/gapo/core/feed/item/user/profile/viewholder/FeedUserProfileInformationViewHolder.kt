package com.gg.gapo.core.feed.item.user.profile.viewholder

import android.os.Bundle
import android.text.Spanned
import android.view.View
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import com.gg.gapo.core.feed.R
import com.gg.gapo.core.feed.databinding.FeedUserProfileInformationItemBinding
import com.gg.gapo.core.feed.item.FeedViewHolder
import com.gg.gapo.core.feed.item.user.profile.model.FeedUserProfileInformationViewData
import com.gg.gapo.core.feed.utils.removeOnClickListener
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.GapoFonts
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.Balloon
import com.skydoves.balloon.BalloonAnimation
import com.skydoves.balloon.BalloonSizeSpec

/**
 * <AUTHOR>
 * @since 24/05/2021
 */
internal class FeedUserProfileInformationViewHolder(
    private val binding: FeedUserProfileInformationItemBinding
) : FeedViewHolder<FeedUserProfileInformationItemBinding, FeedUserProfileInformationViewData>(
    binding
) {

    private var balloon: Balloon? = null

    override fun onBind(item: FeedUserProfileInformationViewData, bundle: Bundle?) {
        setId(item.information)

        binding.textInformation.setDebouncedClickListener {
            if (item.information.tooltipStr.isNotEmpty()) {
                val balloon =
                    createMoreInfoTextTooltips(item.information.tooltipStr).build()
                        .also { balloon -> this.balloon = balloon }
                balloon.showAlignTop(it)
            } else if (item.information is FeedUserProfileInformationViewData.Information.PhoneNumber ||
                item.information is FeedUserProfileInformationViewData.Information.Email ||
                item.information is FeedUserProfileInformationViewData.Information.CustomInfo
            ) {
                item.interactor.onUserProfileClickOnInformation(item.information)
            } else {
                item.interactor.onUserProfileClickOnInformationEndIcon(item.information)
            }
        }

        binding.imageRight.setDebouncedClickListener {
            item.interactor.onUserProfileClickOnInformationEndIcon(item.information)
        }

        if (bundle == null) {
            setTextColor(item.information.textColor)
            setText(item.information.spanned)
            setIcon(item.information)
            setMoreInfo(item.information)
        } else {
            binding.textMore.visibility = View.GONE
            if (bundle.containsKey(FeedUserProfileInformationViewData.TEXT_COLOR_CHANGED_EXTRA)) {
                setTextColor(item.information.textColor)
            }

            if (bundle.containsKey(FeedUserProfileInformationViewData.ICON_CHANGED_EXTRA)) {
                setIcon(item.information)
            }

            if (bundle.containsKey(FeedUserProfileInformationViewData.TEXT_CONTENT_EXTRA)) {
                setText(item.information.spanned)
            }

            if (bundle.containsKey(FeedUserProfileInformationViewData.MORE_INFO_CHANGE_EXTRA)) {
                setMoreInfo(item.information)
            }
        }
    }

    override fun onViewRecycled() {
        binding.textInformation.removeOnClickListener()

        if (balloon?.isShowing == true) {
            balloon?.dismiss()
        }
        balloon = null
    }

    /**
     * Set id để show tooltips
     */
    private fun setId(information: FeedUserProfileInformationViewData.Information) {
        itemView.id = when (information) {
            is FeedUserProfileInformationViewData.Information.Department -> R.id.feed_user_profile_information_department_id
            else -> View.NO_ID
        }
    }

    private fun setTextColor(@ColorRes color: Int) {
        binding.textInformation.setTextColor(ContextCompat.getColor(context, color))
    }

    private fun setText(text: Spanned) {
        binding.textInformation.post {
            binding.textInformation.setText(text, TextView.BufferType.SPANNABLE)
        }
    }

    private fun setIcon(information: FeedUserProfileInformationViewData.Information) {
        binding.imageIcon.setImageResource(information.startIcon)

        binding.imageRight.isVisible = information.endIcon != 0
        binding.imageRight.setImageResource(information.endIcon)

        if (information !is FeedUserProfileInformationViewData.Information.Department) {
            binding.imageIcon.setColorFilter(
                ContextCompat.getColor(
                    context,
                    GapoColors.contentQuaternary
                ),
                android.graphics.PorterDuff.Mode.SRC_IN
            )
        } else {
            binding.imageIcon.clearColorFilter()
        }
    }

    private fun setMoreInfo(information: FeedUserProfileInformationViewData.Information) {
        binding.textMore.post {
            when (information) {
                is FeedUserProfileInformationViewData.Information.Department -> {
                    setTextMoreInfoDepartment(information)
                }
                is FeedUserProfileInformationViewData.Information.CustomInfo -> {
                    setTextMoreInfoCustom(information)
                }
                else -> {
                    binding.textMore.isVisible = false
                }
            }
        }
    }

    private fun setTextMoreInfoDepartment(item: FeedUserProfileInformationViewData.Information.Department) {
        binding.textMore.isVisible = item.role?.isNotEmpty() == true

        binding.textMore.text = item.role
    }

    private fun setTextMoreInfoCustom(item: FeedUserProfileInformationViewData.Information.CustomInfo) {
        binding.textMore.isVisible = item.title.isNotEmpty() == true

        binding.textMore.text = item.title
    }

    private fun createMoreInfoTextTooltips(text: String): Balloon.Builder {
        val builder = Balloon.Builder(context)
            .setWidth(BalloonSizeSpec.WRAP)
            .setHeight(BalloonSizeSpec.WRAP)
            .setPadding(10)
            .setText(text)
            .setTextColorResource(GapoColors.contentInversePrimary)
            .setTextSize(14f)
            .setArrowPositionRules(ArrowPositionRules.ALIGN_BALLOON)
            .setArrowSize(6)
            .setArrowPosition(0.5f)
            .setCornerRadius(4f)
            .setBackgroundColorResource(GapoColors.bgInversePrimary)
            .setBalloonAnimation(BalloonAnimation.OVERSHOOT)
            .setDismissWhenClicked(true)
            .setDismissWhenTouchOutside(true)
        val font = ResourcesCompat.getFont(context, GapoFonts.font_roboto_regular)
        if (font != null) {
            builder.setTextTypeface(font)
        }
        return builder
    }
}
