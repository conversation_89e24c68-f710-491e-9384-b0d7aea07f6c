package com.gg.gapo.core.workspace.domain.usecase

import com.gg.gapo.core.workspace.domain.WorkspaceRepository
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 09/02/2022
 */
internal class JoinWorkspaceUseCase(
    private val repository: WorkspaceRepository
) {
    suspend operator fun invoke(workspaceId: String) {
        try {
            repository.joinWorkspace(workspaceId)
        } catch (e: Exception) {
            Timber.e(e)
        }
    }
}
