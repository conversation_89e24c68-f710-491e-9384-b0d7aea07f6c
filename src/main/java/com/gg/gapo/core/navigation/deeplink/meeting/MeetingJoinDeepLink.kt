package com.gg.gapo.core.navigation.deeplink.meeting

import androidx.core.os.bundleOf
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink

/**
 * <AUTHOR>
 * @since 21/12/2021
 */
class MeetingJoinDeepLink(
    val meetingId: String,
    override val options: GapoDeepLink.Options? = null
) : GapoDeepLink {

    override val link: String
        get() = "meet/$meetingId"

    companion object {
        const val USE_ZOOM_APP_EXTRA = "use_zoom_app"

        fun createBundle(useZoomApp: Boolean) =
            bundleOf(
                USE_ZOOM_APP_EXTRA to useZoomApp
            )
    }
}
