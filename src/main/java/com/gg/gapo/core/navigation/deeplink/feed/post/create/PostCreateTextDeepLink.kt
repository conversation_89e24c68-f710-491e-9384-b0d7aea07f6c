package com.gg.gapo.core.navigation.deeplink.feed.post.create

import androidx.core.os.bundleOf
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink

/**
 * <AUTHOR>
 * @since 21/12/2021
 */
class PostCreateTextDeepLink(
    override val options: GapoDeepLink.Options? = null
) : GapoDeepLink {
    override val link: String
        get() = "post/creator/create?open_type=${PostCreateDeepLinkType.TEXT.value}"

    companion object {
        const val LINK_EXTRA = "link"
        private const val EXTRA_SOURCE = "source"

        fun createLinkBundle(link: String) =
            bundleOf(
                LINK_EXTRA to link
            )

        fun createLinkBundle(source: PostCreateSource) =
            bundleOf(
                EXTRA_SOURCE to source.value
            )
    }
}
