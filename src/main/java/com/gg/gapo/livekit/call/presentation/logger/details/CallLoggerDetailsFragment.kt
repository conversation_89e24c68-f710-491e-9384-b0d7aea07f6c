package com.gg.gapo.livekit.call.presentation.logger.details

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.DividerItemDecoration
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.call.databinding.CallLoggerDetailsFragmentBinding
import com.gg.gapo.livekit.call.presentation.logger.CallLoggerViewModel
import com.gg.gapo.livekit.call.presentation.logger.details.adapter.CallLoggerDetailsAdapter
import com.gg.gapo.livekit.call.presentation.logger.toDisplayTitle
import org.koin.androidx.viewmodel.ext.android.activityViewModel

class CallLoggerDetailsFragment : Fragment() {

    private val viewModel by activityViewModel<CallLoggerViewModel>()

    private var _binding by autoCleared<CallLoggerDetailsFragmentBinding>()
    private val adapter by lazy {
        CallLoggerDetailsAdapter {
            // nothings
        }
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        _binding = CallLoggerDetailsFragmentBinding.inflate(inflater, container, false)
        return _binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        _binding.recyclerView.adapter = adapter
        _binding.buttonCopy.setDebouncedClickListener {
            context?.copyToClipboard(viewModel.copyContent())
        }
        _binding.btnBack.setDebouncedClickListener {
            findNavController().popBackStack()
        }
        viewModel.steps.observe(viewLifecycleOwner) {
            adapter.addData(it)
        }
        viewModel.log.observe(viewLifecycleOwner) {
            _binding.textTitle.text = it.toDisplayTitle()
            _binding.textDescription.text = it.callId
        }
        _binding.recyclerView.addItemDecoration(
            DividerItemDecoration(
                this.requireContext(),
                DividerItemDecoration.VERTICAL
            )
        )
    }
}

private fun Context.copyToClipboard(content: String) {
    val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
    val clip: ClipData = ClipData.newPlainText("call_logger", content)
    clipboard?.setPrimaryClip(clip)
    GapoToast.makeNormal(this, GapoStrings.common_copy_to_clip_board).show()
}
