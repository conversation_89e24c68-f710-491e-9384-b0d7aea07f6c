package com.gg.gapo.feature.v2.presentation.common

import android.content.Context
import android.graphics.Rect
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity

/**
 * <AUTHOR>
 * @since 06/05/2022
 */
abstract class HideKeyboardBaseActivity : GapoThemeBaseActivity() {

    private var focusedViewOnActionDown: View? = null
    private var touchWasInsideFocusedView = false

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                focusedViewOnActionDown = currentFocus
                val focusedViewOnActionDown = focusedViewOnActionDown
                if (focusedViewOnActionDown != null) {
                    val rect = Rect()
                    val coordinates = IntArray(2)
                    focusedViewOnActionDown.getLocationOnScreen(coordinates)
                    rect[coordinates[0], coordinates[1], coordinates[0] + focusedViewOnActionDown.width] =
                        coordinates[1] + focusedViewOnActionDown.height
                    val x = ev.x.toInt()
                    val y = ev.y.toInt()
                    touchWasInsideFocusedView = rect.contains(x, y)
                }
            }
            MotionEvent.ACTION_UP -> if (focusedViewOnActionDown != null) {
                // dispatch to allow new view to (potentially) take focus
                val consumed = super.dispatchTouchEvent(ev)
                val currentFocus = currentFocus

                // if the focus is still on the original view and the touch was inside that view,
                // leave the keyboard open.  Otherwise, if the focus is now on another view and that view
                // is an EditText, also leave the keyboard open.
                if (currentFocus == focusedViewOnActionDown) {
                    if (touchWasInsideFocusedView) {
                        return consumed
                    }
                } else if (currentFocus is EditText) {
                    return consumed
                }

                // the touch was outside the originally focused view and not inside another EditText,
                // so close the keyboard
                val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(focusedViewOnActionDown?.windowToken, 0)
                focusedViewOnActionDown?.clearFocus()
                return consumed
            }
        }
        return super.dispatchTouchEvent(ev)
    }
}
