package com.gg.gapo.feature.v2.presentation.order.forms

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.ConcatAdapter
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.databinding.isVisible
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.approval.databinding.OrderListFormApprovalFragmentBinding
import com.gg.gapo.feature.v2.presentation.model.enum.FromRequestEnum
import com.gg.gapo.feature.v2.presentation.order.bottomsheet.select.OrderSelectBottomSheetFragment.Companion.REQUEST_FORM_TYPE
import com.gg.gapo.feature.v2.presentation.order.bottomsheet.select.OrderSelectBottomSheetFragment.Companion.SELECTED_ID_BUNDLE
import com.gg.gapo.feature.v2.presentation.order.bottomsheet.select.OrderSelectBottomSheetFragment.Companion.SELECT_BS_KEY
import com.gg.gapo.feature.v2.presentation.order.forms.ListFormViewModel.Companion.ID_ITEM_CATEGORY_ALL
import com.gg.gapo.feature.v2.presentation.order.forms.adapter.LastEditCategoryAdapter
import com.gg.gapo.feature.v2.presentation.order.forms.adapter.ListCategoryAdapter
import com.gg.gapo.feature.v2.presentation.utils.ConstUtils.ZERO_VALUE
import com.gg.gapo.feature.v2.presentation.utils.NavUtils.navFinish
import com.gg.gapo.feature.v2.presentation.utils.NavUtils.navigate
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 12/5/22
 */
internal class ListFormFragment : Fragment() {
    private var binding by autoCleared<OrderListFormApprovalFragmentBinding>()
    private val viewModel by viewModel<ListFormViewModel>()

    private var categoriesAdapter by autoCleared<ListCategoryAdapter>()
    private var recentCategoriesAdapter by autoCleared<ListCategoryAdapter>()
    private var lastEditCategoryAdapter by autoCleared<LastEditCategoryAdapter>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = OrderListFormApprovalFragmentBinding.inflate(inflater, container, false)

        viewModel.fetchCategory()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initLayout()
        initObserver()

        setFragmentResultListener(SELECT_BS_KEY) { _, bundle ->
            val result = bundle.getString(SELECTED_ID_BUNDLE)
            if (result != null) {
                viewModel.setCategoryIdSelected(result)
            }
        }
    }

    private fun initLayout() {
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.fetchCategory()
            binding.swipeRefreshLayout.isRefreshing = false
        }

        binding.imageBack.setDebouncedClickListener {
            navFinish()
        }

        binding.imageMixer.setDebouncedClickListener {
            val data = viewModel.listCategoryOriginalFlow.value
            navigate(
                ListFormFragmentDirections.actionGlobalToFilterFragment(
                    REQUEST_FORM_TYPE,
                    viewModel.getCategoryIdSelected(),
                    (data ?: emptyList()).toTypedArray()
                )
            )
        }

        binding.inputSearch.doAfterTextChanged {
            viewModel.searchForm(it.toString())
        }

        categoriesAdapter = ListCategoryAdapter(requireContext()) {
            navigateFragment(it, FromRequestEnum.SETUP.value)
        }

        recentCategoriesAdapter = ListCategoryAdapter(requireContext()) {
            navigateFragment(it, FromRequestEnum.SETUP.value)
        }

        lastEditCategoryAdapter = LastEditCategoryAdapter {
            navigate(
                ListFormFragmentDirections.actionGlobalToFormDetailFragment(
                    it,
                    FromRequestEnum.LAST_EDIT.value
                )
            )
        }

        binding.listForm.adapter =
            ConcatAdapter(lastEditCategoryAdapter, recentCategoriesAdapter, categoriesAdapter)
    }

    private fun initObserver() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.listCategoryOriginalFlow.collect {
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.categoryFilterSelectedFlow.collect {
                    binding.imageMarkFilter.isVisible =
                        it.isNotEmpty() && it != ID_ITEM_CATEGORY_ALL
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.listRecentCategoriesViewDataFlow.collect { data ->
                    if (data != null) {
                        recentCategoriesAdapter.submitList(
                            data.map {
                                it.copy(
                                    name = getString(GapoStrings.new_approval_recomend_title)
                                )
                            }
                        )
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.listLastEditCategoriesFlow.collect { data ->
                    if (data != null) {
                        lastEditCategoryAdapter.submitList(data.map { it.copy(design = it.design.copy()) })
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.categoriesWithFormFlow.collect { data ->
                    if (data != null) {
                        categoriesAdapter.submitList(data.map { it.copy() })
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.isShowEmptyListFlow.collect {
                    it?.let {
                        binding.groupEmpty.isVisible = it.first
                        binding.textEmpty.text =
                            if (it.second != ZERO_VALUE) getString(it.second) else ""
                    }
                }
            }
        }
    }

    private fun navigateFragment(id: String, type: Int) {
        if (viewModel.isApprovalId(id)) {
            navigate(
                ListFormFragmentDirections.actionGlobalToCalendarChosenFragment(id)
            )
        } else {
            navigate(
                ListFormFragmentDirections.actionGlobalToFormDetailFragment(
                    id,
                    type
                )
            )
        }
    }
}
