package com.gg.gapo.feature.v2.domain.usecase

import com.gg.gapo.feature.v2.domain.model.request.CancelRequestModel
import com.gg.gapo.feature.v2.domain.repo.ApprovalRepository
import kotlinx.coroutines.flow.Flow

/**
 * <AUTHOR>
 * @since 7/5/23
 */
internal class CancelRequestUseCase constructor(private val approvalRepository: ApprovalRepository) {
    suspend operator fun invoke(request: CancelRequestModel): Flow<Boolean> {
        return approvalRepository.cancelRequestForm(request)
    }
}
