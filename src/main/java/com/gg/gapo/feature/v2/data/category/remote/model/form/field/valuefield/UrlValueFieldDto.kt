package com.gg.gapo.feature.v2.data.category.remote.model.form.field.valuefield

import com.gg.gapo.feature.v2.domain.model.response.form.value.UrlValueFieldModel
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @since 3/30/23
 */
internal data class UrlValueFieldDto(
    @SerializedName("store")
    val store: String?,
    @SerializedName("src")
    val src: String?
)

internal object UrlValueFieldMapper {
    fun UrlValueFieldModel.mapToDto() = UrlValueFieldDto(
        store = store,
        src = src
    )
}
