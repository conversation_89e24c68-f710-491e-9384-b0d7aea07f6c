package com.gg.gapo.feature.post.presentation.post.share

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import com.gg.gapo.analytic.ObjectType
import com.gg.gapo.analytic.features.GAPOAnalytics
import com.gg.gapo.core.navigation.deeplink.feed.FeedDeepLinkScreen
import com.gg.gapo.core.ui.bottomsheet.GapoBottomSheetFragment
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.livedata.debounce
import com.gg.gapo.feature.post.databinding.PostShareOptionBottomSheetFragmentBinding
import com.gg.gapo.feature.post.presentation.post.share.viewmodel.PostShareActionViewModel
import com.gg.gapo.feature.post.presentation.utils.navToPostSharingActivity
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.parameter.parametersOf

/**
 * <AUTHOR>
 * @since 06/05/2021
 */
internal class PostShareOptionBottomSheetFragment internal constructor() :
    GapoBottomSheetFragment() {

    private val shareActionViewModel by viewModel<PostShareActionViewModel> {
        parametersOf(
            PostShareActionViewModel.Params(
                requireArguments().getString(POST_ID_ARG, ""),
                requireArguments().getString(USER_ID_ARG, ""),
                requireArguments().getString(SHARED_POST_ID_ARG),
                requireArguments().getString(GROUP_ID_ARG),
                requireArguments().getString(MEDIA_ID_ARG),
                FeedDeepLinkScreen.get(requireArguments().getString(FROM_SCREEN_ARG))
                    ?: FeedDeepLinkScreen.FEED_FOLLOW
            )
        )
    }

    private var binding by autoCleared<PostShareOptionBottomSheetFragmentBinding>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = PostShareOptionBottomSheetFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = shareActionViewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val viewLifecycleOwner = viewLifecycleOwner

        shareActionViewModel.onClickWritePostEventLiveData
            .debounce()
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    GAPOAnalytics.getInstance(requireContext().applicationContext)
                        .logEventShare(
                            it.first,
                            ObjectType.POST,
                            shareActionViewModel.trackingScreen
                        )
                    requireContext().navToPostSharingActivity(it.first, it.second)
                }
            )

        shareActionViewModel.onFinishEventLiveData
            .observe(
                this,
                EventObserver {
                    requireActivity().finish()
                }
            )
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        shareActionViewModel.finish()
    }

    companion object {
        internal val TAG: String = PostShareOptionBottomSheetFragment::class.java.simpleName

        private const val POST_ID_ARG = "POST_ID_ARG"
        private const val USER_ID_ARG = "USER_ID_ARG"
        private const val SHARED_POST_ID_ARG = "SHARED_POST_ID_ARG"
        private const val GROUP_ID_ARG = "GROUP_ID_ARG"
        private const val MEDIA_ID_ARG = "MEDIA_ID_ARG"
        private const val FROM_SCREEN_ARG = "FROM_SCREEN_ARG"

        internal fun newInstance(
            postId: String,
            userId: String,
            sharedPostId: String?,
            groupId: String?,
            mediaId: String?,
            fromScreen: String?
        ) = PostShareOptionBottomSheetFragment()
            .apply {
                arguments = bundleOf(
                    POST_ID_ARG to postId,
                    USER_ID_ARG to userId,
                    SHARED_POST_ID_ARG to sharedPostId,
                    GROUP_ID_ARG to groupId,
                    MEDIA_ID_ARG to mediaId,
                    FROM_SCREEN_ARG to fromScreen
                )
            }
    }
}
