package com.gg.gapo.feature.survey.common;

import androidx.annotation.Nullable;

/**
 * Utilities for generating 64-bit long IDs from types such as {@link CharSequence}.
 */
public final class AdapterIdUtils {
    private AdapterIdUtils() {
    }

    /**
     * Hash a long into 64 bits instead of the normal 32. This uses a xor shift implementation to
     * attempt pseudo randomness so object ids have an even spread for less chance of collisions.
     * <p>
     * From http://stackoverflow.com/a/11554034
     * <p>
     * http://www.javamex.com/tutorials/random_numbers/xorshift.shtml
     */
    public static long hashLong64Bit(long value) {
        value ^= (value << 21);
        value ^= (value >>> 35);
        value ^= (value << 4);
        return value;
    }

    /**
     * Hash a string into 64 bits instead of the normal 32. This allows us to better use strings as a
     * model id with less chance of collisions. This uses the FNV-1a algorithm for a good mix of speed
     * and distribution.
     * <p>
     * Performance comparisons found at http://stackoverflow.com/a/1660613
     * <p>
     * Hash implementation from http://www.isthe.com/chongo/tech/comp/fnv/index.html#FNV-1a
     */
    public static long hashString64Bit(@Nullable CharSequence str) {
        if (str == null) {
            return 0;
        }

        long result = 0xcbf29ce484222325L;
        final int len = str.length();
        for (int i = 0; i < len; i++) {
            result ^= str.charAt(i);
            result *= 0x100000001b3L;
        }
        return result;
    }
}