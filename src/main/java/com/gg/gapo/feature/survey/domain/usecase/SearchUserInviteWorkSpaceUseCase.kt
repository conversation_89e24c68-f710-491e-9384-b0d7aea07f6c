package com.gg.gapo.feature.survey.domain.usecase

import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.survey.domain.SurveyRepository
import com.gg.gapo.feature.survey.domain.model.PagingDataResponseModel
import com.gg.gapo.feature.survey.domain.model.UserProfileDataModel

internal class SearchUserInviteWorkSpaceUseCase(private val repository: SurveyRepository) {

    suspend operator fun invoke(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<UserProfileDataModel>> {
        return repository.searchUserInviteWorkSpace(queryMap)
    }
}
