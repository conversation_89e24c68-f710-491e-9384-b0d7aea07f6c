package com.gg.gapo.feature.messenger.domain.messenger.usecase.message

import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.messenger.domain.messenger.MessengerRepository
import com.gg.gapo.feature.messenger.domain.messenger.model.common.MessengerExceptionHandler
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import timber.log.Timber

internal class FetchPreviewLinkUseCase(
    private val messengerRepository: MessengerRepository,
    private val exceptionHandler: MessengerExceptionHandler
) {
    suspend operator fun invoke(url: String): Result<MessageModel.MessagePreviewLinkModel> {
        return try {
            val result = messengerRepository.fetchPreviewLink(url)
            if (result == null) {
                Result.Error(Exception())
            } else {
                Result.Success(result)
            }
        } catch (e: Exception) {
            Timber.e(e)
            Result.Error(exceptionHandler(e))
        }
    }
}
