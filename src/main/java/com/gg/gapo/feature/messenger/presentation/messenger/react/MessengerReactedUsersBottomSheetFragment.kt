package com.gg.gapo.feature.messenger.presentation.messenger.react

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.LinearLayoutManager
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.bottomsheet.GapoHeightLimitedBottomSheetFragment
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.glide.GlideRequests
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.livedata.debounce
import com.gg.gapo.core.utilities.view.recyclerview.InfiniteScrollListener
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.widget.reaction.MessengerReactType
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.widget.reaction.mapToMessengerMessageReactViewData
import com.gg.gapo.feature.messenger.presentation.messenger.react.adapter.ReactedUserAdapter
import com.gg.gapo.feature.messenger.presentation.messenger.react.decoration.ReactedUserDecoration
import com.gg.gapo.feature.messenger.presentation.messenger.react.viewmodel.ReactedUsersViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewModel
import com.gg.gapo.messenger.databinding.MessengerReactedUsersBottomSheetFragmentBinding
import com.google.android.material.tabs.TabLayout
import org.koin.androidx.viewmodel.ext.android.activityViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * Created by bacnd on 06/09/2022.
 */
internal class MessengerReactedUsersBottomSheetFragment internal constructor() :
    GapoHeightLimitedBottomSheetFragment(),
    TabLayout.OnTabSelectedListener {

    private val reactedUsersViewModel by viewModel<ReactedUsersViewModel>()
    private val messengerViewModel by activityViewModel<MessengerViewModel>()

    private var binding by autoCleared<MessengerReactedUsersBottomSheetFragmentBinding> {
        it.tabLayout.removeOnTabSelectedListener(this)
        it.listReactedUsers.clearOnScrollListeners()
    }

    private var reactedUserAdapter by autoCleared<ReactedUserAdapter>()

    private var glideRequests by autoCleared<GlideRequests>()

    private var conversationId: Long? = 0
    private var messageId: Int? = 0
    private var listener: MessengerReactedUsersBottomSheetListener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        listener = parentFragment as? MessengerReactedUsersBottomSheetListener ?: activity as? MessengerReactedUsersBottomSheetListener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = MessengerReactedUsersBottomSheetFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = reactedUsersViewModel
        glideRequests = GapoGlide.with(this)

        initRcvReactedUsers()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val viewLifecycleOwner = viewLifecycleOwner

        conversationId = arguments?.getLong(CONVERSATION_ID_ARG)
        messageId = arguments?.getInt(MESSAGE_ID_ARG)
        if (conversationId == null || messageId == null) {
            dismissAllowingStateLoss()
            return
        }

        reactedUsersViewModel.messageReactLiveData.observe(viewLifecycleOwner) {
            setupTabLayout(it)
        }

        reactedUsersViewModel.reactedUsersLiveData
            .observe(viewLifecycleOwner) {
                reactedUserAdapter.submitList(it) {
                    reactedUsersViewModel.onReactedUsersSubmitted()
                }
            }

        reactedUsersViewModel.onReactedUsersSubmittedEventLiveData
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    binding.listReactedUsers.invalidateItemDecorations()
                }
            )

        reactedUsersViewModel.onNavToReactedUserProfileEventLiveData
            .debounce()
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    messengerViewModel.onMessengerMessageOnClickUserAvatar(userId = it)
                    dismissAllowingStateLoss()
                }
            )

        reactedUsersViewModel.onUnReactMySelfEventLiveData
            .debounce()
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    messageId?.let { messageId ->
                        listener?.onUnReactMessage(messageId)
                        dismiss()
                    }
                }
            )

        conversationId?.let { conversationId ->
            messageId?.let { messageId ->
                reactedUsersViewModel.fetch(conversationId, messageId)
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
    }

    override fun onTabSelected(tab: TabLayout.Tab) {
        val status = MessengerReactType.getByType(tab.id)
        status?.let { it ->
            reactedUsersViewModel.onSelectedReactTypeChange(it)
        }
    }

    override fun onTabUnselected(tab: TabLayout.Tab?) {
    }

    override fun onTabReselected(tab: TabLayout.Tab?) {
    }

    private fun setupTabLayout(messageReactModel: MessageModel.MessageReactModel) {
        val reacts = messageReactModel.reactCount.mapToMessengerMessageReactViewData()

        val totalReact = messageReactModel.reactCountTotal

        val totalTab = binding.tabLayout.newTab()
            .apply {
                id = MessengerReactType.UNKNOWN.type
                text = getString(GapoStrings.feeds_reacted_users_total_format, totalReact)
            }
        binding.tabLayout.addTab(totalTab)

        reacts.forEach { react ->
            if (react.count > 0) {
                val newTab = binding.tabLayout.newTab()
                    .apply {
                        val icon = react.type.res
                        setIcon(icon)

                        id = react.type.type
                        text = react.count.toString()
                    }
                binding.tabLayout.addTab(newTab)
            }
        }

        binding.tabLayout.addOnTabSelectedListener(this)
    }

    private fun initRcvReactedUsers() {
        reactedUserAdapter = ReactedUserAdapter(requireContext(), reactedUsersViewModel, glideRequests)
        val linearLayoutManager = LinearLayoutManager(requireContext())
        val reactedUserDecoration = ReactedUserDecoration(requireContext())
        binding.listReactedUsers.run {
            setHasFixedSize(true)
            itemAnimator = null
            layoutManager = linearLayoutManager
            addItemDecoration(reactedUserDecoration)
            addOnScrollListener(object : InfiniteScrollListener(linearLayoutManager) {

                override fun isDataLoading(): Boolean {
                    return reactedUsersViewModel.isReactedUsersFetching
                }

                override fun onLoadMore() {
                    reactedUsersViewModel.fetchMore(messageId ?: 0)
                }
            })
            adapter = reactedUserAdapter
        }
    }

    internal interface MessengerReactedUsersBottomSheetListener {
        fun onUnReactMessage(messageId: Int)
    }

    companion object {
        internal val TAG = MessengerReactedUsersBottomSheetFragment::class.java.simpleName
        private const val CONVERSATION_ID_ARG = "CONVERSATION_ID_ARG"
        private const val MESSAGE_ID_ARG = "MESSAGE_ID_ARG"

        fun createInstance(conversationId: Long, messageId: Int) = MessengerReactedUsersBottomSheetFragment().apply {
            arguments = bundleOf(
                CONVERSATION_ID_ARG to conversationId,
                MESSAGE_ID_ARG to messageId
            )
        }
    }
}
