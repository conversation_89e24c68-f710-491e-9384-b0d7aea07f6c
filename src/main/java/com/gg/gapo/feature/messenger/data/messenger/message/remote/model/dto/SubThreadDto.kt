package com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto

import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageUserModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.SubThreadModel
import com.google.gson.annotations.SerializedName

/**
 * Created by bacnd on 21/02/2023.
 */
data class SubThreadDto(
    @SerializedName("id") var id: Long?,
    @SerializedName("type") var type: String?,
    @SerializedName("name") var name: String?,
    @SerializedName("avatar") var avatar: String?,
    @SerializedName("collab_id") var collabId: String?,
    @SerializedName("member_count") var memberCount: Int?,
    @SerializedName("message_count") var messageCount: Int?,
    @SerializedName("parent_id") var parentId: Long?,
    @SerializedName("parent_thread_type") var parentThreadType: String?,
    @SerializedName("unread_count") val unReadCount: Int?,
    @SerializedName("root_message_id") val rootMessageId: Int?,
    @SerializedName("tags") val tags: List<String>?,
    @SerializedName("commenters") var commenters: List<String>?
)

internal fun SubThreadDto.mapToDomain(): SubThreadModel {
    return SubThreadModel(
        id ?: 0L,
        type.orEmpty(),
        name.orEmpty(),
        avatar.orEmpty(),
        collabId.orEmpty(),
        memberCount ?: 0,
        messageCount ?: 0,
        parentId ?: 0L,
        parentThreadType.orEmpty(),
        unReadCount ?: 0,
        tags.orEmpty(),
        commenters?.map { userId -> MessageUserModel(id = userId) } ?: emptyList()
    )
}
