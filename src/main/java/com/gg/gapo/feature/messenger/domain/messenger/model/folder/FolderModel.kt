package com.gg.gapo.feature.messenger.domain.messenger.model.folder

import android.annotation.SuppressLint
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.DiffUtil
import com.gg.gapo.feature.messenger.presentation.helper.Constant

/**
 * Created by bacnd on 11/06/2022.
 */
internal data class FolderModel(
    val alias: FolderType,
    val name: String,
    val avatar: String,
    val position: Int,
    val unreadCount: Int
) {
    object FolderModelDiffUtil : DiffUtil.ItemCallback<FolderModel>() {
        override fun areItemsTheSame(oldItem: FolderModel, newItem: FolderModel): Boolean {
            return oldItem.alias == newItem.alias
        }

        @SuppressLint("DiffUtilEquals")
        override fun areContentsTheSame(oldItem: FolderModel, newItem: FolderModel): Boolean {
            return oldItem == newItem
        }

        override fun getChangePayload(oldItem: FolderModel, newItem: FolderModel): Any {
            val bundle = bundleOf()
            return bundle
        }
    }
}

sealed class FolderType {

    abstract val alias: String

    object Default : FolderType() {
        override val alias: String
            get() = Constant.Default
    }

    object Unread : FolderType() {
        override val alias: String
            get() = Constant.Unread
    }

    object Stranger : FolderType() {
        override val alias: String
            get() = Constant.Stranger
    }

    object Secret : FolderType() {
        override val alias: String
            get() = Constant.Secret
    }

    object SubThread : FolderType() {
        override val alias: String
            get() = Constant.SubThread
    }

    data class Folder(override val alias: String) : FolderType()

    companion object {

        fun getByType(alias: String) = when (alias) {
            Constant.Default -> Default
            Constant.Unread -> Unread
            Constant.Stranger -> Stranger
            Constant.Secret -> Secret
            Constant.SubThread -> SubThread
            else -> Folder(alias = alias)
        }
    }
}
