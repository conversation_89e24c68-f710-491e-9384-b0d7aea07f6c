package com.gg.gapo.feature.messenger.presentation.messenger.widget.quickreplies

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.databinding.BindingAdapter
import androidx.lifecycle.LiveData
import androidx.lifecycle.findViewTreeLifecycleOwner
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import com.gg.gapo.feature.messenger.presentation.messenger.widget.quickreplies.adapter.QuickRepliesMessageAdapter
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.databinding.MessengerQuickRepliesMessageViewBinding
import com.google.android.flexbox.*

/**
 * Created by bacnd on 08/12/2022.
 */
internal class MessengerQuickRepliesMessageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr), QuickRepliesMessageAdapter.QuickRepliesListener {

    private val binding = MessengerQuickRepliesMessageViewBinding.inflate(LayoutInflater.from(context), this, true)
    private var interactor: Interactor? = null
    private lateinit var quickRepliesMessageAdapter: QuickRepliesMessageAdapter

    init {
    }

    override fun onDetachedFromWindow() {
        interactor = null
        super.onDetachedFromWindow()
    }

    fun setInteractor(interactor: Interactor) {
        this.interactor = interactor
        val viewLifecycleOwner = findViewTreeLifecycleOwner() ?: return
        binding.lifecycleOwner = viewLifecycleOwner

        quickRepliesMessageAdapter = QuickRepliesMessageAdapter(context, this)

        val layoutManager = FlexboxLayoutManager(context)
        layoutManager.flexWrap = FlexWrap.WRAP
        layoutManager.flexDirection = FlexDirection.ROW
        layoutManager.justifyContent = JustifyContent.CENTER
        layoutManager.alignItems = AlignItems.CENTER
        val itemDecoration = FlexboxItemDecoration(context)
        itemDecoration.setOrientation(FlexboxItemDecoration.BOTH)
        ContextCompat.getDrawable(context, R.drawable.messenger_divider_8dp)?.let { itemDecoration.setDrawable(it) }
        if (binding.listQuickReplies.itemDecorationCount > 0) {
            binding.listQuickReplies.removeItemDecorationAt(0)
        }
        binding.listQuickReplies.addItemDecoration(itemDecoration)
        binding.listQuickReplies.layoutManager = layoutManager
        binding.listQuickReplies.adapter = quickRepliesMessageAdapter

        interactor.conversationLiveData.observe(viewLifecycleOwner) { conversation ->
            quickRepliesMessageAdapter.submitList(conversation?.lastMessage?.body?.metadata?.options.orEmpty())
        }
    }

    override fun onClickQuickReplies(option: MessageModel.MessageBodyMetadataOptionModel) {
        interactor?.onMessengerQuickReplySend(option)
    }

    interface Interactor {
        val conversationLiveData: LiveData<ConversationModel?>

        fun onMessengerQuickReplySend(option: MessageModel.MessageBodyMetadataOptionModel)
    }
}

@BindingAdapter("interactor")
internal fun MessengerQuickRepliesMessageView.setInteractorBinding(
    interactor: MessengerQuickRepliesMessageView.Interactor?
) {
    if (interactor != null) {
        setInteractor(interactor)
    }
}
