package com.gg.gapo.feature.messenger.presentation.messenger.widget.input.edittext

import android.content.Context
import android.text.Editable
import android.text.Spanned
import android.util.AttributeSet
import android.view.KeyEvent
import android.widget.EditText
import androidx.emoji2.widget.EmojiEditText
import com.gg.gapo.core.ui.GapoAttributes
import com.gg.gapo.core.utilities.regex.GapoUrlRegex
import com.gg.gapo.core.utilities.resources.GapoGlobalResources
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.edittext.style.MessengerEditTextHyperLinkRule
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.edittext.style.MessengerEditTextMentionRule
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.edittext.style.MessengerEditTextRule
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.edittext.style.MessengerEditTextTextMatcher
import com.gg.gapo.feature.messenger.utils.safeSubstring
import com.gg.gapo.feature.messenger.utils.setEndSelection
import timber.log.Timber
import kotlin.math.max

/**
 * <AUTHOR>
 * @since 15/08/2022
 */
internal class MessengerEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : EmojiEditText(context, attrs, defStyleAttr),
    MessengerEditTextTextMatcher.OnMatcherListener {

    private val mentions = mutableListOf<MessengerMention>()

    private val backgroundColor = GapoGlobalResources.getColor(context, GapoAttributes.positiveSecondary)
    private val textColor = GapoGlobalResources.getColor(context, GapoAttributes.accentWorkSecondary)

    private var matcher: MessengerEditTextTextMatcher? = null
    private val mentionRule = MessengerEditTextMentionRule(textColor, backgroundColor)
    private val hyperLinkRule = MessengerEditTextHyperLinkRule(textColor)
    private var matchListener: OnMatchListener? = null
    private var backListener: OnBackListener? = null

    init {
        matcher = MessengerEditTextTextMatcher(listOf(mentionRule, hyperLinkRule), this)
        addTextChangedListener(matcher)
    }

    fun setOnMatchListener(listener: OnMatchListener?) {
        if (matchListener == null) {
            matchListener = listener
        }
    }

    fun setOnBackPressListener(listener: OnBackListener) {
        backListener = listener
    }

    fun getMentions() = mentions.toList()

    fun addMention(mentionId: String, mentionText: String) {
        matcher?.isMatchingEnabled = false
        val offset = getMentionOffset()
        if (offset != -1 && replace(mentionText)) {
            mentions.add(MessengerMention(mentionId, mentionText, offset))
            onApplyStyle(editableText)
        }
        matcher?.isMatchingEnabled = true
    }

    fun setContent(text: String?, postContentMentions: List<MessengerMention>) {
        matcher?.isMatchingEnabled = false
        mentions.clear()
        mentions.addAll(postContentMentions)
        setText(text)
        setEndSelection()
        matcher?.isMatchingEnabled = true
    }

    fun clearContent() {
        text?.clear()
        mentions.clear()
    }

    fun getLinks(): List<String> {
        val text = text
        return text.split(SPACE).filter { it.isNotBlank() }.map { word ->
            GapoUrlRegex.WEB_URL.toRegex().findAll(word).toList().map { it.value }
        }.flatten()
    }

    fun performWithoutMatcher(action: EditText.() -> Unit) {
        matcher?.isMatchingEnabled = false
        this.action()
        matcher?.isMatchingEnabled = true
    }

    override fun onKeyPreIme(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && backListener?.invoke() == true) {
            return true
        }
        return super.onKeyPreIme(keyCode, event)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        removeTextChangedListener(matcher)
        matcher = null
        matchListener = null
        backListener = null
    }

    override fun onMatched(rule: MessengerEditTextRule, textMatched: String?) {
        matchListener?.invoke(rule, textMatched)
    }

    /**
     * apply style for mentions
     */
    override fun onApplyStyle(editable: Editable) {
        val style = mentionRule.style ?: return
        // clear previous styles in case targets are invalidated
        editable.getSpans(0, editable.length, style::class.java)
            .forEach {
                editable.removeSpan(it)
            }

        mentions.forEach {
            try {
                editable.setSpan(
                    style.clone(),
                    it.offset,
                    it.offset + it.mentionText.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            } catch (e: Exception) {
                Timber.e(e)
            }
        }
    }

    /**
     * update position for mentions when editing
     */
    override fun onUpdatePosition(start: Int, before: Int, count: Int) {
        with(mentions.iterator()) {
            forEach { mention ->
                val startPosition = start + before

                val endPosition = if (before > count) {
                    (mention.offset + mention.mentionText.length + 1)
                } else {
                    (mention.offset + mention.mentionText.length)
                }

                if (startPosition > mention.offset && startPosition < endPosition) {
                    remove()
                } else if (startPosition <= mention.offset) {
                    mention.offset += (count - before)
                }
            }
        }
    }

    private fun getMentionOffset(): Int {
        val position = max(0, selectionStart - 1)
        return mentionRule.getTargetStart(editableText, position)
    }

    /**
     * Replaces matching target in selection with [newText]
     *
     * Not: does nothing if there's no matching target in selection
     */
    private fun replace(newText: String): Boolean {
        matcher?.let {
            val editable = editableText
            if (editable.isNullOrEmpty()) {
                setText("$newText ")
                return true
            }

            it.rules.forEach { rule ->
                // find closest target's boundaries from selection
                val position = max(0, selectionStart - 1)
                val start = rule.getTargetStart(editable, position)
                val end = rule.getTargetEnd(editable, position)
                val target = editable.safeSubstring(start, end)

                if (rule.isMatches(target)) {
                    editable.replace(start, end, newText)
                    // add whitespace at the end if needed
                    val cursor = start + newText.length
                    val following = editable.getOrNull(cursor)
                    if (following == null || !following.isWhitespace()) {
                        editable.insert(cursor, " ")
                    }
                    return true
                }
            }

            return false
        }

        return false
    }

    companion object {
        private const val SPACE = " "
    }
}

internal typealias OnMatchListener = (MessengerEditTextRule, String?) -> Unit
internal typealias OnBackListener = () -> Boolean
