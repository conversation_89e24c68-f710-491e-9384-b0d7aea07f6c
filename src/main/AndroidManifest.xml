<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.gg.gapo.feature.hashtag">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application>
        <activity
            android:name="com.gg.gapo.feature.hashtag.feed.presentation.FeedHashtagActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Hashtag.Theme"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />

        <activity
            android:name="com.gg.gapo.feature.hashtag.group.presentation.list.hashtag.GroupHashtagActivity"
            android:screenOrientation="portrait"
            android:theme="@style/GapoTheme.NoActionBar" />

        <activity
            android:name="com.gg.gapo.feature.hashtag.group.presentation.feed.GroupFeedHashtagActivity"
            android:screenOrientation="portrait"
            android:theme="@style/GapoTheme.NoActionBar" />
    </application>
</manifest>