<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#B3000000">

    <ImageButton
        android:id="@id/exo_play"
        style="@style/ExoMediaButton.Play"
        android:layout_width="@dimen/_56dp"
        android:layout_height="@dimen/_56dp"
        android:scaleType="centerInside"
        android:src="@drawable/gapo_video_ic_play_48dp_grey"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@id/exo_pause"
        style="@style/ExoMediaButton.Pause"
        android:layout_width="@dimen/_56dp"
        android:layout_height="@dimen/_56dp"
        android:scaleType="centerInside"
        android:src="@drawable/gapo_video_ic_pause_48dp_grey"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@id/exo_position"
        style="@style/GapoTextStyle.BodyMedium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/_8dp"
        android:paddingEnd="@dimen/_8dp"
        android:textColor="@color/alwaysLightPrimary"
        app:layout_constraintBottom_toBottomOf="@+id/layout_exo_progress"
        app:layout_constraintEnd_toStartOf="@+id/layout_exo_progress"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/layout_exo_progress"
        tools:text="00:00" />

    <FrameLayout
        android:id="@+id/layout_exo_progress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/exo_volume"
        app:layout_constraintEnd_toStartOf="@+id/exo_duration"
        app:layout_constraintStart_toEndOf="@+id/exo_position"
        app:layout_constraintTop_toTopOf="@+id/exo_volume">

        <View
            android:id="@id/exo_progress_placeholder"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_20dp" />
    </FrameLayout>

    <TextView
        android:id="@id/exo_duration"
        style="@style/GapoTextStyle.BodyMedium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/_8dp"
        android:paddingEnd="@dimen/_8dp"
        android:textColor="@color/alwaysLightPrimary"
        app:layout_constraintBottom_toBottomOf="@+id/layout_exo_progress"
        app:layout_constraintEnd_toStartOf="@+id/exo_volume"
        app:layout_constraintStart_toEndOf="@+id/layout_exo_progress"
        app:layout_constraintTop_toTopOf="@+id/layout_exo_progress"
        tools:text="00:00" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/exo_volume"
        android:layout_width="@dimen/_40dp"
        android:layout_height="@dimen/_40dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:layout_marginBottom="@dimen/_12dp"
        android:background="@android:color/transparent"
        android:padding="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@drawable/gapo_video_ic_volume_state" />

</androidx.constraintlayout.widget.ConstraintLayout>