<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="121dp"
    android:height="120dp"
    android:viewportWidth="121"
    android:viewportHeight="120">
  <path
      android:pathData="M60.5,0L60.5,0A60,60 0,0 1,120.5 60L120.5,60A60,60 0,0 1,60.5 120L60.5,120A60,60 0,0 1,0.5 60L0.5,60A60,60 0,0 1,60.5 0z"
      android:fillColor="#FFEECF"/>
  <path
      android:pathData="M87.48,38.63L61.5,30.81C60.85,30.62 60.15,30.62 59.5,30.81L33.52,38.63C32.12,39.05 31.17,40.3 31.17,41.71C31.17,76.31 49.31,84.58 59.06,89.02C59.52,89.23 60.01,89.33 60.5,89.33C60.99,89.33 61.48,89.23 61.93,89.02C71.69,84.58 89.83,76.31 89.83,41.71C89.83,40.3 88.88,39.05 87.48,38.63Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="60.5"
          android:startY="30.67"
          android:endX="60.5"
          android:endY="89.33"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFB55A"/>
        <item android:offset="1" android:color="#FFE67E00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M60.5,56.5C64.5,56.5 66.5,52.75 66.5,48.06C66.5,44.31 64.5,41.5 60.5,41.5C56.5,41.5 54.5,44.31 54.5,48.06C54.5,52.75 56.5,56.5 60.5,56.5Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M67.22,73.66C69.05,73.19 70.6,71.96 71.45,70.28C71.46,70.25 71.47,70.23 71.49,70.21C71.5,70.18 71.51,70.16 71.52,70.14C72.69,67.77 72.29,64.86 70.29,63.14C67.66,60.87 64.24,59.5 60.5,59.5C56.76,59.5 53.33,60.87 50.71,63.14C48.71,64.86 48.31,67.77 49.48,70.14C49.5,70.18 49.53,70.23 49.55,70.28C50.4,71.96 51.95,73.19 53.78,73.66C55.93,74.21 58.18,74.5 60.5,74.5C62.82,74.5 65.07,74.21 67.22,73.66Z"
      android:fillColor="#ffffff"/>
</vector>
