<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="glideRequest"
            type="com.gg.gapo.core.utilities.glide.GlideRequests" />

        <variable
            name="feedFollowViewModel"
            type="com.gg.gapo.feature.feed.follow.presentation.viewmodel.FeedFollowViewModel" />
    </data>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bgSecondary"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/bgPrimary">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <include
                    android:id="@+id/workspace_layout"
                    layout="@layout/switch_work_space_layout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_12dp"
                    android:layout_weight="1"
                    app:feedFollowViewModel="@{feedFollowViewModel}"
                    app:glideRequest="@{glideRequest}"
                    app:workspace="@{feedFollowViewModel.currentWorkspaceLiveData}" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/button_zoom"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="?attr/actionBarSize"
                    android:layout_marginStart="@dimen/_6dp"
                    android:background="@color/transparent"
                    android:onClick="@{() -> feedFollowViewModel.onClickButtonZoom()}"
                    android:scaleType="centerInside"
                    app:isVisible="@{feedFollowViewModel.meetingVisibilityLiveData}"
                    app:srcCompat="@drawable/ic28_shared_meeting" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/button_search"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="?attr/actionBarSize"
                    android:background="@color/transparent"
                    android:onClick="@{() -> feedFollowViewModel.onClickButtonSearch()}"
                    android:scaleType="centerInside"
                    android:tint="@color/contentPrimary"
                    app:srcCompat="@drawable/ic24_line15_magnifyingglass_search" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/button_my_profile"
                    android:layout_width="@dimen/_40dp"
                    android:layout_height="?attr/actionBarSize"
                    android:background="@color/transparent"
                    android:onClick="@{() -> feedFollowViewModel.onClickButtonMyProfile()}"
                    android:scaleType="centerInside"
                    android:tint="@color/contentPrimary"
                    app:srcCompat="@drawable/ic24_line15_person_circle" />

            </androidx.appcompat.widget.LinearLayoutCompat>

        </com.google.android.material.appbar.AppBarLayout>

        <com.gg.gapo.core.ui.swiperefreshlayout.GapoSwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:refreshing="@{feedFollowViewModel.isSwipeRefreshLayoutVisibleLiveData}">

            <com.gg.gapo.core.feed.widget.FeedRecyclerView
                android:id="@+id/list_feed_items"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false" />

        </com.gg.gapo.core.ui.swiperefreshlayout.GapoSwipeRefreshLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>


</layout>