<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <import
            alias="GapoStrings"
            type="com.gg.gapo.core.ui.R.string" />

        <import
            alias="GapoColors"
            type="com.gg.gapo.core.ui.R.color" />

        <variable
            name="submitted"
            type="Boolean" />

        <variable
            name="min"
            type="Integer" />

        <variable
            name="max"
            type="Integer" />

        <variable
            name="scale"
            type="Integer" />

        <variable
            name="position"
            type="Integer" />

        <variable
            name="minTitle"
            type="String" />

        <variable
            name="maxTitle"
            type="String" />


    </data>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ln_point"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_12dp"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/_6dp">

        <com.gg.gapo.feature.survey.presentation.compose.question.widget.PointHorizontalView
            android:id="@+id/rcvScale"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_12dp" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_min_label"
                style="@style/GapoTextAppearance.BodyMedium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_56dp"
                android:layout_weight="0.42"
                android:backgroundTint="@color/white"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:hint="@string/survey_v2_optional"
                android:text="@{minTitle}"
                android:textColor="@color/contentPrimary"
                android:visibility="@{TextUtils.isEmpty(minTitle) ? View.GONE : View.VISIBLE}" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_max_label"
                style="@style/GapoTextAppearance.BodyMedium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.42"
                android:backgroundTint="@color/white"
                android:gravity="center_vertical|end"
                android:hint="@string/survey_v2_optional"
                android:text="@{maxTitle}"
                android:textColor="@color/contentPrimary"
                android:visibility="@{TextUtils.isEmpty(maxTitle) ? View.GONE : View.VISIBLE}" />

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>