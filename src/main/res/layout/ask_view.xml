<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.gg.gapo.core.ui.image.GapoAvatarImageView
        android:id="@+id/image_avatar"
        android:layout_width="@dimen/_32dp"
        android:layout_height="@dimen/_32dp"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/text_username"
        style="@style/GapoTextStyle.HeadingSmall"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:layout_toEndOf="@+id/image_avatar"
        android:maxLines="2"
        android:textColor="@color/contentPrimary"
        app:layout_constraintBottom_toBottomOf="@id/image_avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/image_avatar"
        app:layout_constraintTop_toTopOf="@id/image_avatar"
        tools:text="Kiennnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/edit_content"
        style="@style/GapoTextStyle.BodyLarge"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:background="@null"
        android:gravity="top"
        android:hint="@string/common_hint_ask"
        android:inputType="textMultiLine"
        android:maxLength="500"
        android:padding="@dimen/_12dp"
        android:textColor="@color/contentPrimary"
        android:textColorHint="@color/contentQuaternary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/image_avatar" />

</androidx.constraintlayout.widget.ConstraintLayout>
