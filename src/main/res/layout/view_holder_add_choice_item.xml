<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <variable
            name="isPreview"
            type="Boolean" />

        <variable
            name="onClick"
            type="android.view.View.OnClickListener"/>
    </data>

    <androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/root"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_48dp"
        android:clickable="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="@{isPreview? View.GONE : View.VISIBLE}">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingEnd="@dimen/_16dp"
            android:paddingVertical="@dimen/_16dp"
            android:onClick="@{onClick}"
            android:src="@drawable/ic20_fill_plusmark" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/btn_add_question"
            style="@style/GapoTextStyle.BodyLarge"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:paddingVertical="@dimen/_12dp"
            android:layout_weight="1"
            android:background="@null"
            android:text="@string/periodic_survey_add_choice"
            android:textColor="@color/contentQuaternary"
            tools:text="Thêm tùy chọn" />

    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>