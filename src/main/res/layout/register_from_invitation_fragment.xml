<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="registerFromInvitationViewModel"
            type="com.gg.gapo.feature.auth.presentation.register.from.invitation.RegisterFromInvitationViewModel" />

    </data>

    <com.gg.gapo.feature.auth.presentation.common.components.InterceptTouchConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bgPrimary"
        app:interceptTouch="@{registerFromInvitationViewModel.isLoadingLiveData}">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_register_label"
            style="@style/GapoTextStyle.DisplaySmall"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_64dp"
            android:text="@string/authentication_register_title"
            android:textColor="@color/contentPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_email_or_phone"
            style="@style/GapoTextStyle.HeadingMedium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:drawableStart="@drawable/auth_ic_work_email"
            android:drawablePadding="@dimen/_8dp"
            android:drawableTint="@color/contentPrimary"
            app:layout_constraintTop_toBottomOf="@id/text_register_label"
            tools:text="<EMAIL>" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_register_description"
            style="@style/GapoTextStyle.BodyLarge"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_24dp"
            android:text="@string/auth_improve_password_security_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/text_email_or_phone" />

        <com.gg.gapo.feature.auth.presentation.common.components.AuthInputTextLayout
            android:id="@+id/input_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            app:authDrawableStart="@drawable/auth_ic_lock"
            app:authHint="@string/authentication_register_password_hint"
            app:enableShowHidePassword="true"
            app:layout_constraintTop_toBottomOf="@+id/text_register_description" />

        <com.gg.gapo.feature.auth.presentation.common.components.AuthInputTextLayout
            android:id="@+id/input_confirm_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            app:authDrawableStart="@drawable/auth_ic_confirm_password"
            app:authHint="@string/authentication_register_password_confirm_hint"
            app:enableShowHidePassword="true"
            app:layout_constraintTop_toBottomOf="@+id/input_password" />

        <com.gg.gapo.core.ui.button.GapoAppCompatLoadingButton
            android:id="@+id/button_continue"
            style="@style/GapoButton.Large.AccentWorkPrimary.Loading"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_18dp"
            android:text="@string/authentication_button_continue"
            app:isLoading="@{registerFromInvitationViewModel.isButtonLoadingVisibleLiveData}"
            app:layout_constraintTop_toBottomOf="@id/input_confirm_password" />

        <com.gg.gapo.core.ui.progressbar.GapoLoadingProgressBar
            style="@style/GapoProgressBar.Circular.AccentWorkPrimary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:visibility="@{registerFromInvitationViewModel.isLoadingLiveData &amp;&amp; !registerFromInvitationViewModel.isButtonLoadingVisibleLiveData ? View.VISIBLE : View.GONE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/button_continue" />

    </com.gg.gapo.feature.auth.presentation.common.components.InterceptTouchConstraintLayout>

</layout>