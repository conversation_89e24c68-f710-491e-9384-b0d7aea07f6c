<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/feed_posts_list_filter_bg_bottom_sheet_white_round_right_left"
    android:orientation="vertical"
    tools:context=".presentation.filter.FeedFilterPostListBottomFragment">

    <LinearLayout
        android:id="@+id/linear_all"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_16dp">

        <TextView
            android:id="@+id/text_all"
            style="@style/GapoTextAppearance.BodyLarge"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/alwaysDarkPrimary"
            tools:text="Tất cả bài viết" />

        <ImageView
            android:id="@+id/image_all"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            app:tint="@color/accentWorkSecondary"
            android:src="@drawable/ic16_fill_checkmark"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/linear_groups"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_16dp">

        <TextView
            android:id="@+id/text_groups"
            style="@style/GapoTextAppearance.BodyLarge"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/alwaysDarkPrimary"
            tools:text="Bài viết nhóm" />

        <ImageView
            android:id="@+id/image_groups"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            app:tint="@color/accentWorkSecondary"
            android:src="@drawable/ic16_fill_checkmark"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/linear_following"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_16dp">

        <TextView
            android:id="@+id/text_following"
            style="@style/GapoTextAppearance.BodyLarge"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/alwaysDarkPrimary"
            tools:text="Bài viết từ người theo dõi" />

        <ImageView
            android:id="@+id/image_following"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            android:src="@drawable/ic16_fill_checkmark"
            app:tint="@color/accentWorkSecondary"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

</LinearLayout>