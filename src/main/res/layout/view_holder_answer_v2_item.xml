<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import
            alias="GapoAutoDimens"
            type="htkien.autodimens.R.dimen" />

        <import type="android.view.View" />

        <import type="com.gg.gapo.feature.survey.domain.model.QuestionType" />

        <variable
            name="userName"
            type="String" />

        <variable
            name="dateTime"
            type="String" />

        <variable
            name="content"
            type="android.text.SpannableString" />

        <variable
            name="isFilteringByUser"
            type="Boolean" />

        <variable
            name="questionType"
            type="com.gg.gapo.feature.survey.domain.model.QuestionType" />

    </data>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/alwaysLightPrimary"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingBottom="@dimen/_12dp">

        <com.gg.gapo.core.ui.image.GapoAvatarImageView
            android:id="@+id/image_avatar"
            android:layout_width="@dimen/_40dp"
            android:layout_height="@dimen/_40dp"
            android:layout_marginEnd="@dimen/_8dp"
            android:visibility="@{isFilteringByUser ? View.GONE : View.VISIBLE}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line_header"
            tools:visibility="gone" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/periodic_survey_bg_answer"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/_12dp"
                android:paddingVertical="@dimen/_8dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_name"
                    style="@style/GapoTextStyle.BodySmall.Bold"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@{userName}"
                    android:textColor="@color/contentPrimary"
                    android:visibility="@{isFilteringByUser ? View.GONE : View.VISIBLE}"
                    tools:text="Ngô Thị Lan Phượng" />

                <com.gg.gapo.core.ui.textview.richtext.GapoRichTextView
                    android:id="@+id/tv_answer"
                    style="@style/GapoTextStyle.BodyMedium"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/contentPrimary"
                    tools:text="Truyền cảm hứng về sản phẩm cho tất cả mọi người" />

            </androidx.appcompat.widget.LinearLayoutCompat>

<!--            <include-->
<!--                layout="@layout/layout_answer_attachment"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginTop="@dimen/_12dp"-->
<!--                android:visibility="@{questionType == QuestionType.ATTACHMENT ? View.VISIBLE : View.GONE}" />-->

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/ln_attachment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_12dp"
                android:elevation="@dimen/_2dp"
                android:visibility="@{questionType == QuestionType.ATTACHMENT ? View.VISIBLE : View.GONE}"
                app:cardCornerRadius="@dimen/roundMedium"
                app:cardElevation="@dimen/_2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="false"
                app:contentPaddingBottom="@dimen/_12dp"
                app:contentPaddingLeft="@dimen/_10dp"
                app:contentPaddingRight="@dimen/_10dp"
                app:contentPaddingTop="@dimen/_12dp"
                app:strokeColor="@color/linePrimary"
                app:strokeWidth="@dimen/_1dp">

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <com.gg.gapo.core.ui.button.GapoCircularProgressImageButton
                        android:id="@+id/circular_progress_button"
                        android:layout_width="@dimen/_48dp"
                        android:layout_height="@dimen/_48dp"
                        app:backgroundDrawableRes="@drawable/survey_attachment_bg_circle_blue"
                        app:borderWidth="@dimen/_3dp"
                        app:cancelColor="@color/alwaysLightPrimary"
                        app:curveColor="@color/white_alpha_50" />

                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:layout_marginEnd="@dimen/_10dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_file_name"
                            style="@style/GapoTextStyle.HeadingSmall"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:lines="1"
                            android:singleLine="true"
                            android:textColor="@color/contentPrimary"
                            tools:text="Chú_chym_nhỏ.pdf" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/text_description"
                            style="@style/GapoTextStyle.BodySmall"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="@dimen/_4dp"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:lines="1"
                            android:singleLine="true"
                            app:drawableStartCompat="@drawable/ic12_downloaded_small_green"
                            android:textColor="@color/contentSecondary"
                            tools:text="128 MB" />

                    </androidx.appcompat.widget.LinearLayoutCompat>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/button_download"
                        android:layout_width="@dimen/_48dp"
                        android:layout_height="@dimen/_48dp"
                        android:adjustViewBounds="true"
                        android:scaleType="centerInside"
                        app:srcCompat="@drawable/ic24_downloaded_grey" />

                </androidx.appcompat.widget.LinearLayoutCompat>

            </com.google.android.material.card.MaterialCardView>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_date_time"
                style="@style/GapoTextStyle.BodySmall"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_4dp"
                android:text="@{dateTime}"
                android:textColor="@color/contentSecondary"
                tools:text="18/10/2021, 14:44" />

        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>
