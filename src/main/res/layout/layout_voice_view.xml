<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <merge
        tools:parentTag="LinearLayout"
        tools:orientation="horizontal"
        tools:background="@drawable/bg_voice_round"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iconState"
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            android:layout_margin="@dimen/_6dp"
            app:srcCompat="@drawable/ic_messenger_voice_pause_outgoing" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/process"
            android:layout_weight="1"
            android:indeterminate="false"
            android:layout_width="0dp"
            app:indicatorColor="?accentWorkSecondary"
            app:trackColor="#24142141"
            android:visibility="gone"
            android:layout_height="wrap_content"
            app:trackCornerRadius="@dimen/_100dp"/>

        <com.visualizer.amplitude.AudioRecordView
            android:id="@+id/audioRecordView"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="@dimen/_24dp"
            app:chunkAlignTo="bottom"
            app:chunkRoundedCorners="true"
            app:chunkSoftTransition="true"
            app:chunkColor="@color/alwaysLightPrimary"
            app:chunkSpace="@dimen/_2dp"
            app:chunkWidth="@dimen/_4dp"
            app:chunkMaxHeight="@dimen/_24dp"
            app:chunkMinHeight="@dimen/_2dp"
            app:direction="leftToRight"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/duration"
            style="@style/GapoTextAppearance.BodySmall.Bold"
            android:layout_width="wrap_content"
            android:layout_margin="@dimen/_6dp"
            android:layout_gravity="center"
            android:layout_height="wrap_content"
            android:textColor="@color/alwaysLightPrimary"
            tools:text="00:00" />

    </merge>
</layout>