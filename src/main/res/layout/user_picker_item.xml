<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.gg.gapo.core.ui.image.GapoAvatarImageView
            android:id="@+id/image_avatar"
            style="@style/GapoImage.Avatar.Circle"
            android:layout_width="@dimen/_64dp"
            android:layout_height="@dimen/_64dp"
            android:layout_marginHorizontal="@dimen/_12dp"
            android:layout_marginVertical="@dimen/_12dp" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/linear_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_12dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_name"
                style="@style/GapoTextStyle.HeadingMedium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/contentPrimary"
                tools:text="Gapo Member" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_role"
                style="@style/GapoTextStyle.BodyMedium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_2dp"
                android:drawableLeft="@drawable/ic_app_ic_job_title_24"
                android:drawablePadding="@dimen/_8dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/contentSecondary"
                tools:text="role" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_department"
                style="@style/GapoTextStyle.BodyMedium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_2dp"
                android:drawableStart="@drawable/ic_app_ic_department_24"
                android:drawablePadding="@dimen/_8dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/contentSecondary"
                tools:text="department" />

        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/checkbox"
            style="@style/GapoSelection.CheckBox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_12dp"
            android:checked="false"
            android:minWidth="0dp"
            android:minHeight="0dp" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>