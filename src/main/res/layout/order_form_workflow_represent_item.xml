<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/_8dp"
    android:paddingHorizontal="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/image_circle_background"
        android:layout_width="@dimen/_32dp"
        android:layout_height="@dimen/_32dp"
        android:tint="@color/bgTertiary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/approval_avatar_bg_white_circle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/image_icon"
        android:layout_width="@dimen/_20dp"
        android:layout_height="@dimen/_20dp"
        android:tint="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/image_circle_background"
        app:layout_constraintEnd_toEndOf="@id/image_circle_background"
        app:layout_constraintStart_toStartOf="@id/image_circle_background"
        app:layout_constraintTop_toTopOf="@id/image_circle_background"
        app:srcCompat="@drawable/approval_ic24_line15_filter" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/image_state_background"
        android:layout_width="@dimen/_17dp"
        android:layout_height="@dimen/_17dp"
        android:background="@color/white"
        android:scaleType="centerCrop"
        android:tint="@color/bgPrimary"
        app:contentPadding="@dimen/_2dp"
        app:layout_constraintBottom_toBottomOf="@id/image_state"
        app:layout_constraintDimensionRatio="H,1:1"
        app:layout_constraintEnd_toEndOf="@id/image_state"
        app:layout_constraintStart_toStartOf="@id/image_state"
        app:layout_constraintTop_toTopOf="@id/image_state"
        app:shapeAppearanceOverlay="@style/ApprovalImageCircle" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/image_state"
        android:layout_width="@dimen/_16dp"
        android:layout_height="@dimen/_16dp"
        android:layout_marginStart="@dimen/_2dp"
        android:layout_marginTop="@dimen/_2dp"
        android:background="?accentWorkSecondary"
        android:scaleType="centerCrop"
        android:tint="@color/bgPrimary"
        app:contentPadding="@dimen/_2dp"
        app:layout_constraintCircle="@id/image_circle_background"
        app:layout_constraintCircleAngle="135"
        app:layout_constraintCircleRadius="@dimen/_16dp"
        app:layout_constraintDimensionRatio="H,1:1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/ApprovalImageCircle"
        app:srcCompat="@drawable/ic12_fill_checkmark" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_image_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="image_state_background,image_state"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/text_name"
        style="@style/GapoTextAppearance.HeadingMedium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:textColor="@color/contentPrimary"
        app:layout_constraintBottom_toTopOf="@id/text_state"
        app:layout_constraintEnd_toStartOf="@id/text_watchers"
        app:layout_constraintTop_toTopOf="@id/image_circle_background"
        app:layout_constraintBottom_toBottomOf="@id/image_circle_background"
        app:layout_constraintStart_toEndOf="@id/image_circle_background"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Bat dau" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/text_state"
        style="@style/GapoTextAppearance.HeadingSmall"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="@dimen/_2dp"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/text_watchers"
        app:layout_constraintStart_toStartOf="@id/text_name"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="state"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        style="@style/GapoTextAppearance.BodyMedium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/approval_bg_edittext_reason_state"
        android:drawablePadding="@dimen/_8dp"
        android:id="@+id/text_watchers"
        android:visibility="gone"
        android:drawableTint="@color/contentSecondary"
        android:paddingHorizontal="@dimen/_12dp"
        android:paddingVertical="@dimen/_6dp"
        android:text="abc"
        app:layout_constraintEnd_toStartOf="@id/image_action"
        android:textColor="@color/contentSecondary"
        app:drawableEndCompat="@drawable/ic20_line15_bell" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/image_action"
        android:layout_width="@dimen/_32dp"
        android:layout_height="@dimen/_32dp"
        android:visibility="gone"
        android:padding="@dimen/_6dp"
        android:tint="@color/contentSecondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/approval_ic_three_dots" />
</androidx.constraintlayout.widget.ConstraintLayout>