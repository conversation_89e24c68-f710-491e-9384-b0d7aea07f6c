<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <View
        android:id="@+id/dividerGray"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/f5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/dividerGreen"
        android:layout_width="@dimen/_4dp"
        android:layout_height="0dp"
        android:background="@color/sticker_green_600"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.aghajari.rlottie.AXrLottieImageView
        android:id="@+id/icon"
        android:layout_width="@dimen/_40dp"
        android:layout_height="@dimen/_40dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/dividerGreen"
        tools:visibility="visible"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/fileExtension"
        style="@style/GapoTextStyle.BodySmall.Bold"
        android:layout_width="@dimen/_40dp"
        android:layout_height="@dimen/_40dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxEms="4"
        android:singleLine="true"
        android:textColor="@color/alwaysLightPrimary"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/icon"
        app:layout_constraintEnd_toEndOf="@+id/icon"
        app:layout_constraintStart_toStartOf="@+id/icon"
        app:layout_constraintTop_toTopOf="@+id/icon"
        tools:text="MP4"
        tools:visibility="visible" />

    <androidx.emoji2.widget.EmojiTextView
        android:id="@+id/tvUserReply"
        style="@style/GapoTextStyle.BodyMedium.Bold"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        android:layout_weight="0.5"
        android:ellipsize="end"
        android:gravity="start"
        android:paddingStart="@dimen/_8dp"
        android:paddingEnd="@dimen/_12dp"
        android:singleLine="true"
        android:textColor="@color/contentPrimary"
        app:layout_constraintBottom_toTopOf="@id/tvContentReply"
        app:layout_constraintEnd_toStartOf="@+id/btnRemove"
        app:layout_constraintStart_toEndOf="@+id/icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Bùi Quang Việt" />

    <androidx.emoji2.widget.EmojiTextView
        android:id="@+id/tvContentReply"
        style="@style/GapoTextStyle.BodyMedium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnRemove"
        app:layout_constraintStart_toEndOf="@+id/icon"
        app:layout_constraintTop_toBottomOf="@id/tvUserReply"
        android:layout_weight="0.5"
        android:ellipsize="end"
        android:gravity="start"
        android:paddingStart="@dimen/_8dp"
        android:paddingEnd="@dimen/_12dp"
        android:singleLine="true"
        android:textColor="@color/contentPrimary"
        tools:text="Nói chung là okie Nói c Nói chung là okie Nói c Nói chung là okie Nói c" />



    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btnRemove"
        android:layout_width="@dimen/_48dp"
        android:layout_height="@dimen/_48dp"
        android:padding="@dimen/_18dp"
        android:tint="@color/contentPrimary"
        android:foreground="?actionBarItemBackground"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_close_12" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btnSeeMore"
        android:layout_width="@dimen/_48dp"
        android:layout_height="@dimen/_48dp"
        android:padding="@dimen/_14dp"
        android:foreground="?actionBarItemBackground"
        android:tint="@color/contentPrimary"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_messenger_arrow_next" />
</merge>